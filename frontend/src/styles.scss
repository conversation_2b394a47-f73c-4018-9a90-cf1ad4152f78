/* You can add global styles to this file, and also import other style files */
@import '@angular/material/prebuilt-themes/indigo-pink.css';
@import 'leaflet/dist/leaflet.css';

/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Roboto', sans-serif;
  overflow-x: hidden; /* Éviter la barre de défilement horizontale */
}

body {
  margin: 0;
  background-color: #fafafa;
}

/* Éviter les barres de défilement multiples */
app-root {
  display: block;
  min-height: 100vh;
}

/* Conteneurs principaux */
.main-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Éviter les hauteurs fixes qui causent des problèmes de défilement */
.dashboard-container,
.admin-dashboard-container,
.nurse-dashboard,
.page-content {
  flex: 1;
  overflow-y: auto;
  /* Suppression des min-height fixes */
}

/* Material Design overrides */
.mat-mdc-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.mat-mdc-button.mat-primary {
  background-color: #3f51b5;
  color: white;
}

.mat-mdc-raised-button.mat-primary {
  background-color: #3f51b5;
  color: white;
}

/* Utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.p-16 {
  padding: 16px;
}

/* Snackbar styles */
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

.warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}
