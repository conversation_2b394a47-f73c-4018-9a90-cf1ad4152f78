import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { User } from '../../models/user.model';
import { Appointment } from '../../models/appointment.model';

export interface NurseSelectionData {
  appointment: Appointment;
  availableNurses: User[];
}

@Component({
  selector: 'app-nurse-selection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatListModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule
  ],
  template: `
    <h2 mat-dialog-title>
      <mat-icon>person_add</mat-icon>
      Sélectionner un infirmier
    </h2>

    <mat-dialog-content>
      <div class="appointment-info">
        <h3>Rendez-vous</h3>
        <div class="info-row">
          <strong>Patient:</strong>
          {{ data.appointment.patient.firstName || 'N/A' }} {{ data.appointment.patient.lastName || 'N/A' }}
        </div>
        <div class="info-row">
          <strong>Date:</strong> 
          {{ data.appointment.scheduledDate | date:'dd/MM/yyyy HH:mm' }}
        </div>
        <div class="info-row">
          <strong>Adresse:</strong> 
          {{ data.appointment.homeAddress }}
        </div>
        <div class="info-row" *ngIf="data.appointment.analysisTypes && data.appointment.analysisTypes.length > 0">
          <strong>Analyses:</strong>
          <mat-chip-set>
            <mat-chip *ngFor="let analysis of data.appointment.analysisTypes">
              {{ analysis.name }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>

      <mat-divider></mat-divider>

      <div class="nurses-section">
        <h3>Infirmiers disponibles ({{ data.availableNurses.length }})</h3>
        
        <mat-selection-list #nurseList [multiple]="false">
          <mat-list-option 
            *ngFor="let nurse of data.availableNurses" 
            [value]="nurse"
            class="nurse-option">
            <div class="nurse-info">
              <div class="nurse-header">
                <mat-icon matListItemIcon>local_hospital</mat-icon>
                <div class="nurse-name">
                  <strong>{{ nurse.firstName }} {{ nurse.lastName }}</strong>
                  <small>{{ nurse.email }}</small>
                </div>
              </div>
              
              <div class="nurse-details">
                <div class="detail-item" *ngIf="nurse.phone">
                  <mat-icon>phone</mat-icon>
                  <span>{{ nurse.phone }}</span>
                </div>
                
                <div class="detail-item" *ngIf="nurse.address">
                  <mat-icon>location_on</mat-icon>
                  <span>{{ nurse.address }}</span>
                </div>
                
                <div class="detail-item">
                  <mat-icon>schedule</mat-icon>
                  <span>Disponible</span>
                </div>
              </div>
            </div>
          </mat-list-option>
        </mat-selection-list>

        <div *ngIf="data.availableNurses.length === 0" class="no-nurses">
          <mat-icon>warning</mat-icon>
          <p>Aucun infirmier disponible pour le moment.</p>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">
        Annuler
      </button>
      
      <button mat-button (click)="onAutoAssign()" color="accent">
        <mat-icon>auto_fix_high</mat-icon>
        Affectation automatique
      </button>
      
      <button
        mat-raised-button
        color="primary"
        (click)="onAssignWithSelection(nurseList)"
        [disabled]="!nurseList.selectedOptions.hasValue()">
        <mat-icon>check</mat-icon>
        Affecter l'infirmier sélectionné
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .appointment-info {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .appointment-info h3 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .info-row {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .info-row strong {
      min-width: 80px;
    }

    .nurses-section h3 {
      margin: 16px 0 12px 0;
      color: #1976d2;
    }

    .nurse-option {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 12px;
      padding: 8px;
    }

    .nurse-option:hover {
      background-color: #f5f5f5;
    }

    .nurse-option.mat-mdc-list-option-selected {
      background-color: #e3f2fd;
      border-color: #1976d2;
    }

    .nurse-info {
      width: 100%;
    }

    .nurse-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;
    }

    .nurse-name {
      display: flex;
      flex-direction: column;
    }

    .nurse-name small {
      color: #666;
      font-size: 12px;
    }

    .nurse-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-left: 36px;
    }

    .detail-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #666;
    }

    .detail-item mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .no-nurses {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .no-nurses mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #ff9800;
      margin-bottom: 16px;
    }

    mat-dialog-content {
      max-height: 70vh;
      overflow-y: auto;
    }

    mat-dialog-actions {
      padding: 16px 24px;
      gap: 8px;
    }

    @media (max-width: 600px) {
      .nurse-details {
        margin-left: 0;
        margin-top: 8px;
      }

      .nurse-header {
        flex-direction: column;
        align-items: flex-start;
      }

      mat-dialog-actions {
        flex-direction: column;
      }

      mat-dialog-actions button {
        width: 100%;
        margin: 4px 0;
      }
    }
  `]
})
export class NurseSelectionDialogComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<NurseSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: NurseSelectionData
  ) {}

  ngOnInit(): void {
    // Trier les infirmiers par nom
    this.data.availableNurses.sort((a, b) => 
      `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
    );
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAutoAssign(): void {
    this.dialogRef.close({ action: 'auto-assign' });
  }

  onAssign(): void {
    // Cette méthode sera appelée depuis le template avec la référence
    // Nous la modifierons pour recevoir la sélection en paramètre
  }

  onAssignWithSelection(nurseList: any): void {
    const selectedOptions = nurseList.selectedOptions.selected;
    if (selectedOptions.length > 0) {
      const selectedNurse = selectedOptions[0].value;
      this.dialogRef.close({
        action: 'manual-assign',
        nurse: selectedNurse
      });
    }
  }
}
