import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';
import { AppointmentService } from '../../services/appointment.service';
import { GeolocationService, LocationResult } from '../../services/geolocation.service';
import { LocationSharingService, LocationSharingStatus } from '../../services/location-sharing.service';
import { ResultsService, AppointmentResult } from '../../services/results.service';
import { User } from '../../models/user.model';
import { Appointment, AppointmentStatus } from '../../models/appointment.model';
import { Subscription, interval, of } from 'rxjs';
import { timeout, catchError } from 'rxjs/operators';
import { HeaderComponent } from '../header/header.component';
import { environment } from '../../../environments/environment';

interface WeeklyStats {
  totalMissions: number;
  completedMissions: number;
  pendingMissions: number;
  totalDistance: number;
}

@Component({
  selector: 'app-nurse-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule, MatSnackBarModule],
  templateUrl: './nurse-dashboard.component.html',
  styleUrls: ['./nurse-dashboard.component.css']
})
export class NurseDashboardComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  appointments: Appointment[] = [];
  weeklyStats: WeeklyStats = {
    totalMissions: 0,
    completedMissions: 0,
    pendingMissions: 0,
    totalDistance: 0
  };

  // Modal state
  showNotesModal: boolean = false;
  showLocationModal: boolean = false;
  showSamplingConfirmModal: boolean = false;
  showResultsModal: boolean = false;
  currentNotes: string = '';
  selectedAppointment: Appointment | null = null;
  samplingAppointment: Appointment | null = null;
  resultsAppointment: Appointment | null = null;

  // Results upload state
  selectedFiles: File[] = [];
  resultComments: string = '';
  isUploadingResults: boolean = false;

  // UI state
  isAvailable: boolean = true;
  showMap: boolean = false;

  // Propriétés calculées pour éviter les erreurs de détection de changement
  urgentCount: number = 0;
  pendingCount: number = 0;
  todayAppointments: Appointment[] = [];
  completedToday: number = 0;

  // État de chargement
  isLoading: boolean = true;

  // Partage de position
  locationSharingStatus: LocationSharingStatus = { isActive: false };
  isLocationSharingEnabled: { [appointmentId: number]: boolean } = {};

  // Stockage local pour persister l'état entre les rechargements
  private readonly LOCATION_SHARING_STORAGE_KEY = 'nurse_location_sharing_states';

  // Geolocation
  private locationSubscription?: Subscription;
  private currentPosition: LocationResult | null = null;
  private activeWatchId?: number; // ID du watchPosition pour le suivi GPS continu

  private subscriptions = new Subscription();

  constructor(
    private authService: AuthService,
    private appointmentService: AppointmentService,
    private geolocationService: GeolocationService,
    private locationSharingService: LocationSharingService,
    private resultsService: ResultsService,
    private cdr: ChangeDetectorRef,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    console.log('NgOnInit - Nurse Dashboard');

    // Vérifier d'abord si l'utilisateur est déjà connecté
    const currentUser = this.authService.getCurrentUser();
    if (currentUser) {
      console.log('User already logged in:', currentUser);
      this.currentUser = currentUser;
      this.loadAppointments();
      this.initializeLocationSharingStates();
    }

    // Écouter les changements d'utilisateur pour les futures connexions
    this.authService.currentUser$.subscribe(user => {
      console.log('User changed:', user);
      if (user && user !== this.currentUser) {
        this.currentUser = user;
        this.loadAppointments();
        this.initializeLocationSharingStates();
      } else if (!user) {
        console.log('User logged out, clearing appointments');
        this.currentUser = null;
        this.appointments = [];
        this.calculateStats();
        this.calculateWeeklyStats();
        this.isLoading = false;
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.stopLocationSharing();
  }

  // Chargement des données
  loadAppointments(): void {
    console.log('Loading appointments...');
    this.isLoading = true;

    if (this.currentUser) {
      this.appointmentService.getNurseAppointments()
        .pipe(
          timeout(5000), // Timeout après 5 secondes
          catchError((error) => {
            console.error('Error loading appointments:', error);
            // En cas d'erreur, retourner un tableau vide
            return of([]);
          })
        )
        .subscribe({
          next: (appointments) => {
            console.log('Appointments loaded from API:', appointments);
            this.appointments = appointments || [];

            // Debug: Vérifier l'état samplingCompleted
            appointments.forEach(app => {
              console.log(`📋 Appointment ${app.id}: status=${app.status}, samplingCompleted=${app.samplingCompleted}, locationSharingEnabled=${app.locationSharingEnabled}`);
            });

            this.calculateStats();
            this.calculateWeeklyStats();
            this.isLoading = false;

            // Initialiser l'état du partage de position après le chargement
            this.initializeLocationSharingStates();
          },
          error: (error) => {
            console.error('Error loading appointments:', error);
            this.isLoading = false;
            // En cas d'erreur, initialiser avec un tableau vide
            this.appointments = [];
            this.calculateStats();
            this.calculateWeeklyStats();
          }
        });
    } else {
      console.log('No current user found');
      this.isLoading = false;
      // Si pas d'utilisateur, initialiser avec un tableau vide
      this.appointments = [];
      this.calculateStats();
      this.calculateWeeklyStats();
    }
  }

  private calculateStats(): void {
    // Utiliser setTimeout pour éviter les erreurs de détection de changement
    setTimeout(() => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      this.todayAppointments = this.appointments.filter(app => {
        const appDate = new Date(app.scheduledDate);
        appDate.setHours(0, 0, 0, 0);
        return appDate.getTime() === today.getTime();
      });

      this.urgentCount = this.appointments.filter(app => app.isUrgent).length;
      this.pendingCount = this.appointments.filter(app =>
        app.status === 'NURSE_ASSIGNED' || app.status === 'CONFIRMED'
      ).length;
      this.completedToday = this.todayAppointments.filter(app =>
        app.status === 'SAMPLING_DONE' || app.status === 'COMPLETED'
      ).length;

      // Forcer la détection de changement
      this.cdr.detectChanges();
    }, 0);
  }

  refreshMissions(): void {
    console.log('Refresh missions clicked');
    this.loadAppointments();
  }

  calculateWeeklyStats(): void {
    const now = new Date();
    const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
    
    const weeklyAppointments = this.appointments.filter(apt => {
      const aptDate = new Date(apt.scheduledDate);
      return aptDate >= weekStart;
    });

    this.weeklyStats = {
      totalMissions: weeklyAppointments.length,
      completedMissions: weeklyAppointments.filter(apt => 
        apt.status === AppointmentStatus.COMPLETED || apt.status === AppointmentStatus.SAMPLING_DONE
      ).length,
      pendingMissions: weeklyAppointments.filter(apt => 
        apt.status === AppointmentStatus.NURSE_ASSIGNED || apt.status === AppointmentStatus.NURSE_ON_WAY
      ).length,
      totalDistance: this.calculateTotalDistance(weeklyAppointments)
    };
  }

  private calculateTotalDistance(appointments: Appointment[]): number {
    // Calcul approximatif basé sur les positions des patients
    return appointments.length * 15; // 15km en moyenne par mission
  }

  // Méthodes utilitaires
  formatTime(date: string | Date): string {
    return new Date(date).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getMissionCardClass(appointment: Appointment): string {
    switch (appointment.status) {
      case AppointmentStatus.NURSE_ASSIGNED:
        return 'assigned';
      case AppointmentStatus.NURSE_ON_WAY:
        return 'on-way';
      case AppointmentStatus.IN_PROGRESS:
        return 'in-progress';
      case AppointmentStatus.SAMPLING_DONE:
      case AppointmentStatus.COMPLETED:
        return 'completed';
      default:
        return '';
    }
  }

  getStatusClass(status: AppointmentStatus): string {
    switch (status) {
      case AppointmentStatus.NURSE_ASSIGNED:
        return 'assigned';
      case AppointmentStatus.NURSE_ON_WAY:
        return 'on-way';
      case AppointmentStatus.IN_PROGRESS:
        return 'in-progress';
      case AppointmentStatus.SAMPLING_DONE:
      case AppointmentStatus.COMPLETED:
        return 'completed';
      default:
        return '';
    }
  }

  getStatusLabel(status: AppointmentStatus): string {
    switch (status) {
      case AppointmentStatus.NURSE_ASSIGNED:
        return 'Assigné';
      case AppointmentStatus.NURSE_ON_WAY:
        return 'En route';
      case AppointmentStatus.IN_PROGRESS:
        return 'En cours';
      case AppointmentStatus.SAMPLING_DONE:
        return 'Prélèvement effectué';
      case AppointmentStatus.COMPLETED:
        return 'Terminé';
      default:
        return status;
    }
  }

  calculateDistance(lat: number, lng: number): number {
    if (!this.currentPosition || !this.currentPosition.success || !this.currentPosition.coordinates) {
      return 0;
    }

    const R = 6371; // Rayon de la Terre en km
    const dLat = this.deg2rad(lat - this.currentPosition.coordinates.latitude);
    const dLon = this.deg2rad(lng - this.currentPosition.coordinates.longitude);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(this.currentPosition.coordinates.latitude)) *
      Math.cos(this.deg2rad(lat)) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }

  // Actions de mission
  startMission(appointment: Appointment): void {
    console.log('🚀 Starting mission for appointment:', appointment.id);
    console.log('📋 Appointment details:', appointment);

    // 1. NE PAS changer le statut immédiatement - attendre la confirmation
    // this.updateAppointmentToOnWay(appointment); // SUPPRIMÉ

    // 2. Afficher la modale de confirmation pour le partage de position
    this.selectedAppointment = appointment;
    this.showLocationModal = true;

    console.log('✅ Mission modal opened - Waiting for user confirmation. Status still:', appointment.status);
  }

  // Méthode pour mettre le statut en "En route"
  private updateAppointmentToOnWay(appointment: Appointment): void {
    console.log('🚗 Updating appointment status to NURSE_ON_WAY:', appointment.id);

    // Mettre à jour localement
    appointment.status = AppointmentStatus.NURSE_ON_WAY;

    // Mettre à jour dans la liste
    const appointmentIndex = this.appointments.findIndex(app => app.id === appointment.id);
    if (appointmentIndex !== -1) {
      this.appointments[appointmentIndex].status = AppointmentStatus.NURSE_ON_WAY;
    }

    // Sauvegarder en base de données
    this.appointmentService.updateAppointmentStatus(appointment.id, AppointmentStatus.NURSE_ON_WAY).subscribe({
      next: (updatedAppointment) => {
        console.log('✅ Appointment status updated to NURSE_ON_WAY:', updatedAppointment);
      },
      error: (error) => {
        console.error('❌ Error updating appointment status:', error);
      }
    });
  }

  confirmLocationSharing(): void {
    if (!this.selectedAppointment) return;

    console.log('User confirmed location sharing');

    // Démarrer le partage de position avec le nouveau service
    this.locationSharingService.startLocationSharing(this.selectedAppointment.id).subscribe({
      next: (success) => {
        console.log('📥 Location sharing service response:', success);
        console.log('📥 Type of success:', typeof success);
        console.log('📥 Success === true:', success === true);
        console.log('🔍 Selected appointment before update:', this.selectedAppointment);
        console.log('🔍 Selected appointment exists:', !!this.selectedAppointment);

        if (success === true && this.selectedAppointment) {
          console.log('✅ Location sharing service succeeded, updating appointment status...');

          // Mettre à jour directement l'état local (rester sur NURSE_ON_WAY)
          this.selectedAppointment.status = AppointmentStatus.NURSE_ON_WAY;
          this.selectedAppointment.locationSharingEnabled = true;
          this.isLocationSharingEnabled[this.selectedAppointment.id] = true;

          // Mettre à jour aussi dans la liste des rendez-vous
          const appointmentIndex = this.appointments.findIndex(a => a.id === this.selectedAppointment!.id);
          if (appointmentIndex !== -1) {
            this.appointments[appointmentIndex].locationSharingEnabled = true;
            this.appointments[appointmentIndex].status = AppointmentStatus.NURSE_ON_WAY;
          }

          // Sauvegarder l'état dans le stockage local
          this.saveLocationSharingStates();

          // Démarrer le suivi temps réel
          this.startRealTimeTracking(this.selectedAppointment);

          // Forcer la détection de changement
          this.cdr.detectChanges();

          // Afficher un message à l'utilisateur
          this.snackBar.open(
            '✅ Partage de position activé ! Le patient peut maintenant voir votre avancement.',
            'Fermer',
            { duration: 4000 }
          );

          console.log('✅ Location sharing state updated locally');
          console.log('   - selectedAppointment.locationSharingEnabled:', this.selectedAppointment.locationSharingEnabled);
          console.log('   - isLocationSharingEnabled[' + this.selectedAppointment.id + ']:', this.isLocationSharingEnabled[this.selectedAppointment.id]);

          // Persister le statut en base de données (endpoint qui fonctionne)
          this.appointmentService.updateAppointmentStatus(this.selectedAppointment.id, AppointmentStatus.NURSE_ON_WAY).subscribe({
            next: (updatedAppointment) => {
              console.log('✅ Backend status updated successfully:', updatedAppointment);
              console.log('📋 Confirmed DB state - Status:', updatedAppointment.status);

              // Mettre à jour l'objet local avec les données de la base
              this.selectedAppointment!.status = updatedAppointment.status;

              // Mettre à jour aussi dans la liste
              const appointmentIndex = this.appointments.findIndex(a => a.id === this.selectedAppointment!.id);
              if (appointmentIndex !== -1) {
                this.appointments[appointmentIndex].status = updatedAppointment.status;
                this.appointments[appointmentIndex].locationSharingEnabled = true; // Force local
              }

              // Sauvegarder à nouveau l'état local après confirmation DB
              this.saveLocationSharingStates();
              this.cdr.detectChanges();
            },
            error: (error) => {
              console.warn('⚠️ Warning: Could not update backend status:', error);
            }
          });

          // Fermer la modal après succès avec un petit délai
          setTimeout(() => {
            console.log('🔒 Closing location modal...');
            this.closeLocationModal();
            console.log('🔒 Modal closed. showLocationModal:', this.showLocationModal);
          }, 100);
        } else {
          console.error('❌ Location sharing service failed or no selected appointment');
          // Fermer la modal même en cas d'échec
          setTimeout(() => {
            console.log('🔒 Closing modal after failure...');
            this.closeLocationModal();
          }, 100);
        }
      },
      error: (error) => {
        console.error('❌ Error starting location sharing:', error);
        // Fallback: passer directement à IN_PROGRESS sans partage
        if (this.selectedAppointment) {
          console.log('🔄 Fallback: updating to IN_PROGRESS without sharing');
          this.selectedAppointment.status = AppointmentStatus.IN_PROGRESS;
          this.selectedAppointment.locationSharingEnabled = false;
          this.isLocationSharingEnabled[this.selectedAppointment.id] = false;

          const appointmentIndex = this.appointments.findIndex(app => app.id === this.selectedAppointment!.id);
          if (appointmentIndex !== -1) {
            this.appointments[appointmentIndex].status = AppointmentStatus.IN_PROGRESS;
            this.appointments[appointmentIndex].locationSharingEnabled = false;
          }

          this.cdr.detectChanges();
        }

        // Fermer la modal après erreur
        setTimeout(() => {
          console.log('🔒 Closing modal after error...');
          this.closeLocationModal();
        }, 100);
      }
    });
  }

  declineLocationSharing(): void {
    if (!this.selectedAppointment) return;

    console.log('🚫 User declined location sharing - updating to NURSE_ON_WAY without sharing');

    // Changer le statut à NURSE_ON_WAY mais SANS partage de position
    if (this.selectedAppointment) {
      console.log('🔄 Updating appointment status to NURSE_ON_WAY (no location sharing)...');

      // Mettre à jour le statut et désactiver le partage
      this.selectedAppointment.status = AppointmentStatus.NURSE_ON_WAY;
      this.selectedAppointment.locationSharingEnabled = false;
      this.isLocationSharingEnabled[this.selectedAppointment.id] = false;

      // Mettre à jour aussi le rendez-vous dans la liste
      const appointmentIndex = this.appointments.findIndex(app => app.id === this.selectedAppointment!.id);
      if (appointmentIndex !== -1) {
        this.appointments[appointmentIndex].status = AppointmentStatus.NURSE_ON_WAY;
        this.appointments[appointmentIndex].locationSharingEnabled = false;
        console.log('📝 Updated appointment in list (no sharing):', this.appointments[appointmentIndex]);
      }

      // Sauvegarder l'état dans le stockage local
      this.saveLocationSharingStates();

      // Mettre à jour le statut dans la base de données
      this.appointmentService.updateAppointmentStatus(this.selectedAppointment.id, AppointmentStatus.NURSE_ON_WAY).subscribe({
        next: (updatedAppointment) => {
          console.log('✅ Appointment status updated to NURSE_ON_WAY (no sharing):', updatedAppointment);
        },
        error: (error) => {
          console.error('❌ Error updating appointment status:', error);
        }
      });

      console.log('🚗 Mission started with NURSE_ON_WAY status (no location sharing)');

      // Forcer la détection de changement
      this.cdr.detectChanges();
    }

    this.closeLocationModal();
  }



  closeLocationModal(): void {
    console.log('🔒 closeLocationModal called');
    console.log('🔒 Before: showLocationModal =', this.showLocationModal);
    this.showLocationModal = false;
    this.selectedAppointment = null;
    console.log('🔒 After: showLocationModal =', this.showLocationModal);

    // Forcer la détection de changement pour la fermeture de la modal
    this.cdr.detectChanges();
  }

  arriveAtPatient(appointment: Appointment): void {
    this.appointmentService.updateAppointmentStatus(appointment.id, AppointmentStatus.IN_PROGRESS).subscribe({
      next: (_updated) => {
        appointment.status = AppointmentStatus.IN_PROGRESS;
        // Arrêter le partage de position
        this.stopLocationSharing();
      },
      error: (error) => {
        console.error('Error updating appointment status:', error);
      }
    });
  }

  completeSampling(appointment: Appointment): void {
    console.log('✅ Completing sampling for appointment:', appointment.id);

    // Mettre à jour immédiatement l'état local
    appointment.status = AppointmentStatus.SAMPLING_DONE;
    appointment.locationSharingEnabled = false;
    appointment.samplingCompleted = true;
    this.isLocationSharingEnabled[appointment.id] = false;

    // Mettre à jour aussi dans la liste
    const appointmentIndex = this.appointments.findIndex(app => app.id === appointment.id);
    if (appointmentIndex !== -1) {
      this.appointments[appointmentIndex].status = AppointmentStatus.SAMPLING_DONE;
      this.appointments[appointmentIndex].locationSharingEnabled = false;
      this.appointments[appointmentIndex].samplingCompleted = true;
    }

    console.log('✅ Sampling completed locally for appointment:', appointment.id);

    // Forcer la détection de changement
    this.cdr.detectChanges();

    // Sauvegarder en base via AppointmentService (qui fonctionne)
    this.appointmentService.updateAppointmentStatus(appointment.id, AppointmentStatus.SAMPLING_DONE).subscribe({
      next: (updatedAppointment) => {
        console.log('✅ Sampling status saved to database:', updatedAppointment);

        // Arrêter le partage de position si actif
        if (this.isLocationSharingEnabled[appointment.id]) {
          this.stopLocationSharing();
        }
      },
      error: (error) => {
        console.error('❌ Error saving sampling status to database:', error);
        console.log('⚠️ Sampling completed locally but may not be persisted');

        // Arrêter le partage de position même en cas d'erreur
        if (this.isLocationSharingEnabled[appointment.id]) {
          this.stopLocationSharing();
        }
      }
    });
  }

  // Gestion des notes
  addNotes(appointment: Appointment): void {
    this.selectedAppointment = appointment;
    this.currentNotes = appointment.nurseNotes || '';
    this.showNotesModal = true;
  }

  viewNotes(appointment: Appointment): void {
    this.selectedAppointment = appointment;
    this.currentNotes = appointment.nurseNotes || '';
    this.showNotesModal = true;
  }

  saveNotes(): void {
    if (this.selectedAppointment) {
      this.appointmentService.updateAppointment(this.selectedAppointment.id, { nurseNotes: this.currentNotes }).subscribe({
        next: (_updated: any) => {
          if (this.selectedAppointment) {
            this.selectedAppointment.nurseNotes = this.currentNotes;
          }
          this.closeModal();
        },
        error: (error: any) => {
          console.error('Error saving notes:', error);
        }
      });
    }
  }

  closeModal(): void {
    this.showNotesModal = false;
    this.selectedAppointment = null;
    this.currentNotes = '';
  }

  // Gestion de la modale de confirmation de prélèvement
  confirmSamplingCompletion(): void {
    if (!this.samplingAppointment) return;

    console.log('✅ User confirmed sampling completion for appointment:', this.samplingAppointment.id);

    // Mettre à jour immédiatement l'état local
    this.samplingAppointment.samplingCompleted = true;

    // Mettre à jour aussi dans la liste
    const appointmentIndex = this.appointments.findIndex(app => app.id === this.samplingAppointment!.id);
    if (appointmentIndex !== -1) {
      this.appointments[appointmentIndex].samplingCompleted = true;
    }

    console.log('✅ Sampling marked as completed locally for appointment:', this.samplingAppointment.id);

    // Forcer la détection de changement
    this.cdr.detectChanges();

    // Appeler l'API pour persister en base et arrêter le partage
    this.completeSampling(this.samplingAppointment);

    // Fermer la modale
    this.closeSamplingConfirmModal();
  }

  cancelSamplingCompletion(): void {
    console.log('❌ User cancelled sampling completion');
    // Simplement fermer la modale sans rien faire
    this.closeSamplingConfirmModal();
  }

  closeSamplingConfirmModal(): void {
    this.showSamplingConfirmModal = false;
    this.samplingAppointment = null;

    // Forcer la détection de changement
    this.cdr.detectChanges();
  }

  // Gestion de la modale de dépôt de résultats
  onFileSelected(event: any): void {
    const files: FileList = event.target.files;
    if (files && files.length > 0) {
      // Convertir FileList en Array et filtrer les PDF
      const filesArray = Array.from(files) as File[];
      this.selectedFiles = filesArray.filter((file: File) =>
        file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
      );

      console.log('📁 Files selected:', this.selectedFiles.map(f => f.name));

      if (this.selectedFiles.length !== files.length) {
        alert('Seuls les fichiers PDF sont acceptés. Certains fichiers ont été ignorés.');
      }
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    console.log('🗑️ File removed, remaining files:', this.selectedFiles.map(f => f.name));
  }

  submitResults(): void {
    if (!this.resultsAppointment) return;

    if (this.selectedFiles.length === 0) {
      alert('Veuillez sélectionner au moins un fichier PDF.');
      return;
    }

    console.log('📤 Submitting results for appointment:', this.resultsAppointment.id);
    console.log('📁 Files to upload:', this.selectedFiles.map(f => f.name));
    console.log('💬 Comments:', this.resultComments);

    this.isUploadingResults = true;

    // Simuler l'upload (remplacer par vraie API)
    setTimeout(() => {
      // Mettre à jour le statut du rendez-vous
      if (this.resultsAppointment) {
        this.resultsAppointment.status = AppointmentStatus.RESULTS_AVAILABLE;
        this.resultsAppointment.nurseNotes = this.resultComments;

        // Mettre à jour aussi dans la liste
        const appointmentIndex = this.appointments.findIndex(app => app.id === this.resultsAppointment!.id);
        if (appointmentIndex !== -1) {
          this.appointments[appointmentIndex].status = AppointmentStatus.RESULTS_AVAILABLE;
          this.appointments[appointmentIndex].nurseNotes = this.resultComments;
        }

        console.log('✅ Results uploaded successfully for appointment:', this.resultsAppointment.id);

        // TODO: Appeler l'API réelle pour sauvegarder
        this.appointmentService.updateAppointmentStatus(this.resultsAppointment.id, AppointmentStatus.RESULTS_AVAILABLE).subscribe({
          next: (updatedAppointment) => {
            console.log('✅ Results status saved to database:', updatedAppointment);
          },
          error: (error) => {
            console.error('❌ Error saving results status:', error);
          }
        });
      }

      this.isUploadingResults = false;
      this.closeResultsModal();

      // Proposer de consulter les résultats
      const viewResults = confirm('Résultats déposés avec succès ! Le patient sera notifié.\n\nVoulez-vous consulter les résultats déposés ?');
      if (viewResults) {
        this.viewUploadedResults(this.resultsAppointment);
      }
    }, 2000);
  }

  cancelResultsUpload(): void {
    if (this.isUploadingResults) {
      if (!confirm('Un téléversement est en cours. Êtes-vous sûr de vouloir annuler ?')) {
        return;
      }
    }

    this.closeResultsModal();
  }

  closeResultsModal(): void {
    this.showResultsModal = false;
    this.resultsAppointment = null;
    this.selectedFiles = [];
    this.resultComments = '';
    this.isUploadingResults = false;

    // Reset file input
    const fileInput = document.getElementById('resultsFileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }

    // Forcer la détection de changement
    this.cdr.detectChanges();
  }

  // Actions externes
  contactPatient(phone: string | undefined): void {
    if (phone) {
      window.open(`tel:${phone}`, '_self');
    } else {
      console.warn('No phone number available for this patient');
    }
  }

  openMaps(lat: number, lng: number): void {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
    window.open(url, '_blank');
  }

  // Gestion de la géolocalisation
  private startLocationSharing(appointment: Appointment): void {
    console.log('Starting location sharing for appointment:', appointment.id);

    this.geolocationService.getCurrentPosition().subscribe({
      next: (position) => {
        this.currentPosition = position;
        if (position.success && position.coordinates) {
          console.log('Initial position obtained:', position.coordinates);

          // Démarrer le suivi continu
          this.locationSubscription = interval(30000).subscribe(() => {
            this.updateLocation(appointment);
          });

          // Première mise à jour immédiate
          this.updateLocation(appointment);
        } else {
          console.error('Failed to get position:', position.error);
        }
      },
      error: (error: any) => {
        console.error('Error getting initial position:', error);
      }
    });
  }

  private updateLocation(appointment: Appointment): void {
    this.geolocationService.getCurrentPosition().subscribe({
      next: (position) => {
        this.currentPosition = position;
        if (position.success && position.coordinates) {
          const coords = position.coordinates;

          console.log('Updating location:', coords.latitude, coords.longitude);

          this.appointmentService.updateNurseLocation(
            appointment.id,
            coords.latitude,
            coords.longitude
          ).subscribe({
            next: (_updated: any) => {
              console.log('Location updated successfully');
              appointment.nurseCurrentLatitude = coords.latitude;
              appointment.nurseCurrentLongitude = coords.longitude;
              appointment.locationLastUpdated = new Date();
            },
            error: (error: any) => {
              console.error('Error updating location:', error);
            }
          });
        } else {
          console.error('Failed to get position for update:', position.error);
        }
      },
      error: (error: any) => {
        console.error('Error getting current position:', error);
      }
    });
  }

  private stopLocationSharing(): void {
    if (this.locationSubscription) {
      this.locationSubscription.unsubscribe();
      this.locationSubscription = undefined;
      console.log('Location sharing stopped');
    }
  }

  // Méthodes pour l'interface utilisateur
  toggleAvailability(): void {
    this.isAvailable = !this.isAvailable;
    console.log('Availability toggled:', this.isAvailable);
  }



  reportIssue(appointment: Appointment): void {
    console.log('Report issue for appointment:', appointment.id);
    // Implémentation à ajouter
  }

  // Méthodes pour l'affichage
  formatDateTime(date: Date | string): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR') + ' à ' + d.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
  }

  getTestTypeLabel(testType: string): string {
    const types: { [key: string]: string } = {
      'BLOOD_TEST': 'Analyse de sang',
      'URINE_TEST': 'Analyse d\'urine',
      'GLUCOSE_TEST': 'Test de glycémie',
      'CHOLESTEROL_TEST': 'Test de cholestérol'
    };
    return types[testType] || testType;
  }

  getStatusBadgeClass(status: string): string {
    const classes: { [key: string]: string } = {
      'NURSE_ASSIGNED': 'status-assigned',
      'IN_PROGRESS': 'status-progress',
      'SAMPLE_COLLECTED': 'status-collected',
      'COMPLETED': 'status-completed'
    };
    return classes[status] || 'status-default';
  }

  openNavigation(appointment: Appointment): void {
    console.log('🧭 Opening navigation for appointment:', appointment.id);

    const patientName = appointment.patient ?
      `${appointment.patient.firstName} ${appointment.patient.lastName}` :
      'Patient';

    let destination = '';
    let googleMapsUrl = '';
    let wazeUrl = '';

    // Vérifier si les coordonnées sont disponibles
    if (appointment.latitude && appointment.longitude) {
      console.log('📍 Using coordinates for navigation');
      destination = `${appointment.latitude},${appointment.longitude}`;
      googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${destination}`;
      wazeUrl = `https://waze.com/ul?ll=${destination}&navigate=yes`;
    } else if (appointment.homeAddress) {
      console.log('🏠 Using address for navigation');
      destination = encodeURIComponent(appointment.homeAddress);
      googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${destination}`;
      wazeUrl = `https://waze.com/ul?q=${destination}&navigate=yes`;
    } else {
      console.warn('❌ No coordinates or address available for appointment:', appointment.id);
      alert('❌ Aucune adresse ou coordonnées disponibles pour ce rendez-vous');
      return;
    }

    // Proposer le choix entre Google Maps et Waze avec une interface améliorée
    const message =
      `🧭 Ouvrir l'itinéraire vers ${patientName} ?\n\n` +
      `📍 ${appointment.homeAddress || 'Coordonnées GPS'}\n` +
      `🚗 Distance estimée: ~${this.getEstimatedDistance(appointment)} km\n\n` +
      `Choisissez votre application de navigation :\n` +
      `• OK = Google Maps\n` +
      `• Annuler = Waze`;

    const useGoogleMaps = confirm(message);

    if (useGoogleMaps) {
      console.log('🗺️ Opening Google Maps navigation');
      window.open(googleMapsUrl, '_blank');
    } else {
      console.log('🚗 Opening Waze navigation');
      window.open(wazeUrl, '_blank');
    }

    // Logger l'action pour le suivi
    console.log(`✅ Navigation opened for appointment ${appointment.id} to: ${destination}`);
  }

  getEstimatedDistance(appointment: Appointment): number {
    // Retourner une distance estimée basée sur l'ID pour éviter les changements aléatoires
    // Utiliser l'ID pour générer une distance cohérente
    const baseDistance = (appointment.id * 3) % 20 + 1; // Distance entre 1 et 20 km
    return Math.round(baseDistance);
  }

  callPatient(appointment: Appointment): void {
    console.log('Call patient for appointment:', appointment.id);
    // Implémentation à ajouter
  }

  sendSMS(appointment: Appointment): void {
    console.log('Send SMS for appointment:', appointment.id);
    // Implémentation à ajouter
  }

  shareLocation(appointment: Appointment): void {
    console.log('Share location for appointment:', appointment.id);
    this.showLocationModal = true;
    this.selectedAppointment = appointment;
  }

  // Initialisation de l'état du partage de position depuis la base de données
  private initializeLocationSharingStates(): void {
    console.log('🔄 Initializing location sharing states from database and local storage...');

    // Charger les états depuis le stockage local
    const savedStates = this.loadLocationSharingStates();

    this.appointments.forEach(appointment => {
      // Priorité 1: État sauvegardé localement (plus récent)
      if (savedStates[appointment.id] !== undefined) {
        this.isLocationSharingEnabled[appointment.id] = savedStates[appointment.id];
        console.log(`💾 Restored location sharing state from storage for appointment ${appointment.id}: ${savedStates[appointment.id]}`);
      }
      // Priorité 2: État de la base de données
      else if (appointment.locationSharingEnabled) {
        this.isLocationSharingEnabled[appointment.id] = true;
        console.log(`✅ Location sharing enabled from DB for appointment ${appointment.id}`);
      } else {
        this.isLocationSharingEnabled[appointment.id] = false;
      }

      // Vérifier si l'infirmier est en route avec partage actif
      if (appointment.status === 'NURSE_ON_WAY' && this.isLocationSharingEnabled[appointment.id]) {
        console.log(`🚗 Nurse is on way with active location sharing for appointment ${appointment.id}`);
        // Démarrer le service de géolocalisation temps réel
        this.startRealTimeTracking(appointment);
      }

      // Debug: Afficher l'état du prélèvement
      console.log(`📋 Appointment ${appointment.id}: samplingCompleted=${appointment.samplingCompleted}, status=${appointment.status}`);

      // S'assurer que samplingCompleted est défini
      if (appointment.samplingCompleted === undefined || appointment.samplingCompleted === null) {
        appointment.samplingCompleted = false;
        console.log(`🔧 Set samplingCompleted to false for appointment ${appointment.id}`);
      }
    });

    console.log('📊 Initialized sharing states:', this.isLocationSharingEnabled);

    // Sauvegarder l'état actuel
    this.saveLocationSharingStates();

    // Forcer la détection de changement après l'initialisation
    this.cdr.detectChanges();
  }

  // Méthodes de stockage local
  private loadLocationSharingStates(): { [appointmentId: number]: boolean } {
    try {
      const saved = localStorage.getItem(this.LOCATION_SHARING_STORAGE_KEY);
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.error('Error loading location sharing states:', error);
      return {};
    }
  }

  private saveLocationSharingStates(): void {
    try {
      localStorage.setItem(this.LOCATION_SHARING_STORAGE_KEY, JSON.stringify(this.isLocationSharingEnabled));
      console.log('💾 Location sharing states saved to storage');
    } catch (error) {
      console.error('Error saving location sharing states:', error);
    }
  }

  // Démarrer le suivi temps réel GPS
  private startRealTimeTracking(appointment: Appointment): void {
    console.log('🚀 Starting REAL-TIME GPS tracking for appointment:', appointment.id);

    // Vérifier si la géolocalisation est supportée
    if (!navigator.geolocation) {
      console.error('❌ Geolocation not supported by this browser');
      this.showGeolocationError('Géolocalisation non supportée par ce navigateur');
      return;
    }

    console.log('✅ Geolocation API available, requesting permission...');

    // Options pour un suivi précis et continu
    const options: PositionOptions = {
      enableHighAccuracy: true,  // GPS haute précision
      timeout: 30000,           // 30 secondes timeout
      maximumAge: 5000          // Position pas plus vieille que 5 secondes
    };

    // Démarrer le suivi continu avec watchPosition
    const watchId = navigator.geolocation.watchPosition(
      (position) => {
        console.log('📍 REAL-TIME GPS Position received:', {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          speed: position.coords.speed,
          timestamp: new Date(position.timestamp)
        });

        // Envoyer immédiatement la position au backend
        this.sendRealTimePositionToAPI(appointment, position);
      },
      (error) => {
        console.error('❌ GPS Error:', error);
        let errorMessage = '';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permission de géolocalisation refusée. Veuillez autoriser l\'accès à votre position.';
            console.error('❌ User denied geolocation permission');
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Position non disponible. Vérifiez que le GPS est activé.';
            console.error('❌ Position unavailable');
            break;
          case error.TIMEOUT:
            errorMessage = 'Délai d\'attente dépassé pour obtenir la position.';
            console.error('❌ Geolocation timeout');
            break;
          default:
            errorMessage = 'Erreur de géolocalisation inconnue.';
        }
        this.showGeolocationError(errorMessage);

        // Fallback: utiliser une position par défaut ou demander à l'utilisateur
        this.useFallbackPosition(appointment);
      },
      options
    );

    // Sauvegarder l'ID du watch pour pouvoir l'arrêter plus tard
    this.activeWatchId = watchId;
    console.log('✅ GPS Watch started with ID:', watchId);

    // Aussi envoyer une position initiale
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('📍 Initial GPS position:', position);
        this.sendRealTimePositionToAPI(appointment, position);
      },
      (error) => console.warn('⚠️ Could not get initial position:', error),
      options
    );
  }

  // Envoyer la position GPS temps réel à l'API
  private sendRealTimePositionToAPI(appointment: Appointment, position: GeolocationPosition): void {
    const payload = {
      nurseId: this.currentUser?.id,
      appointmentId: appointment.id,
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy || 10,
      speed: position.coords.speed || 0,
      heading: position.coords.heading || 0,
      status: 'ON_WAY'
      // Note: timestamp est géré automatiquement par le backend
    };

    console.log('📤 Sending REAL-TIME GPS position to API:', payload);

    // Envoyer via fetch API
    fetch(`${environment.apiUrl}/tracking/update-position`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        return response.json();
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    }).then(data => {
      console.log('✅ REAL-TIME position sent successfully:', data);
    }).catch(error => {
      console.error('❌ Error sending REAL-TIME position:', error);
    });
  }

  // Arrêter le suivi GPS temps réel
  private stopRealTimeTracking(): void {
    if (this.activeWatchId !== undefined) {
      navigator.geolocation.clearWatch(this.activeWatchId);
      console.log('🛑 GPS Watch stopped, ID:', this.activeWatchId);
      this.activeWatchId = undefined;
    }
  }

  // Envoyer la position à l'API de suivi temps réel (ancienne méthode)
  private sendPositionToTrackingAPI(appointment: Appointment, position: LocationResult): void {
    if (!position.success || !position.coordinates) {
      console.error('❌ Invalid position data:', position);
      return;
    }

    const payload = {
      nurseId: this.currentUser?.id,
      appointmentId: appointment.id,
      latitude: position.coordinates.latitude,
      longitude: position.coordinates.longitude,
      accuracy: position.coordinates.accuracy || 10,
      speed: 0, // Sera calculé par le service de géolocalisation
      heading: 0,
      status: 'ON_WAY',
      timestamp: new Date().toISOString()
    };

    console.log('📤 Sending position to tracking API:', payload);

    // Envoyer via fetch API
    fetch('/api/tracking/update-position', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    }).then(response => {
      if (response.ok) {
        console.log('✅ Position sent to tracking API successfully');
      } else {
        console.error('❌ Error sending position to tracking API:', response.status);
      }
    }).catch(error => {
      console.error('❌ Network error sending position:', error);
    });
  }

  // Afficher une erreur de géolocalisation à l'utilisateur
  private showGeolocationError(message: string): void {
    console.error('🚨 Geolocation Error:', message);
    this.snackBar.open(
      `🚨 Erreur GPS: ${message}`,
      'Fermer',
      {
        duration: 8000,
        panelClass: ['error-snackbar']
      }
    );
  }

  // Utiliser une position de fallback quand le GPS échoue
  private useFallbackPosition(appointment: Appointment): void {
    console.log('🔄 Using fallback position for appointment:', appointment.id);

    // Utiliser la position de l'infirmier depuis la base de données
    const fallbackPosition = {
      coords: {
        latitude: this.currentUser?.latitude || 48.8566, // Paris par défaut
        longitude: this.currentUser?.longitude || 2.3522,
        accuracy: 1000, // Précision faible pour indiquer que c'est approximatif
        speed: 0,
        heading: 0
      },
      timestamp: Date.now()
    };

    console.log('📍 Using fallback position:', fallbackPosition);

    // Envoyer cette position comme position de départ
    this.sendRealTimePositionToAPI(appointment, fallbackPosition as GeolocationPosition);

    // Informer l'utilisateur
    this.snackBar.open(
      '📍 Position approximative utilisée. Activez le GPS pour une position précise.',
      'Fermer',
      { duration: 5000 }
    );
  }

  // Méthodes utilitaires pour le partage de position
  isLocationSharingActiveForAppointment(appointmentId: number): boolean {
    // Vérifier plusieurs sources pour l'état du partage
    const localState = this.isLocationSharingEnabled[appointmentId];
    const appointment = this.appointments.find(a => a.id === appointmentId);
    const dbState = appointment?.locationSharingEnabled;

    // Priorité: état local > état base de données > false
    let isActive = false;
    if (localState !== undefined) {
      isActive = localState;
    } else if (dbState !== undefined) {
      isActive = dbState;
      // Synchroniser l'état local avec la base de données
      this.isLocationSharingEnabled[appointmentId] = dbState;
    }

    console.log(`🔍 Location sharing check for appointment ${appointmentId}:`);
    console.log(`   - Local state: ${localState}`);
    console.log(`   - DB state: ${dbState}`);
    console.log(`   - Final result: ${isActive}`);
    console.log(`   - Appointment status: ${appointment?.status}`);

    return isActive;
  }

  canStartLocationSharing(appointment: Appointment): boolean {
    return appointment.status === AppointmentStatus.NURSE_ASSIGNED &&
           !this.isLocationSharingActiveForAppointment(appointment.id);
  }

  canStopLocationSharing(appointment: Appointment): boolean {
    return this.isLocationSharingActiveForAppointment(appointment.id) &&
           appointment.status !== AppointmentStatus.SAMPLING_DONE;
  }

  stopLocationSharingForAppointment(appointment: Appointment): void {
    console.log('🛑 Stopping location sharing for appointment:', appointment.id);

    // Demander confirmation avant d'arrêter le partage
    const confirmStop = confirm(
      '🛑 Arrêter le partage de position ?\n\n' +
      'Le patient ne pourra plus suivre votre position en temps réel.\n\n' +
      'Êtes-vous sûr de vouloir continuer ?'
    );

    if (!confirmStop) {
      console.log('❌ Location sharing stop cancelled by user');
      return;
    }

    // Appeler d'abord l'API backend pour persister l'arrêt du partage
    this.appointmentService.stopLocationTracking(appointment.id).subscribe({
      next: (updatedAppointment) => {
        console.log('✅ Backend updated successfully:', updatedAppointment);

        // Mettre à jour les données locales avec la réponse du backend
        appointment.locationSharingEnabled = updatedAppointment.locationSharingEnabled;
        this.isLocationSharingEnabled[appointment.id] = false;

        // Mettre à jour aussi le rendez-vous dans la liste
        const appointmentIndex = this.appointments.findIndex(app => app.id === appointment.id);
        if (appointmentIndex !== -1) {
          this.appointments[appointmentIndex] = { ...updatedAppointment };
          console.log('📝 Updated appointment in list at index:', appointmentIndex);
        }

        // Arrêter le service de partage de position local
        this.locationSharingService.stopLocationSharing(appointment.id).subscribe({
          next: (_success) => {
            console.log('✅ Location sharing service stopped locally');
          },
          error: (error) => {
            console.warn('⚠️ Warning stopping local location service:', error);
          }
        });

        console.log('✅ Location sharing stopped successfully for appointment:', appointment.id);

        // Mettre à jour l'état local
        this.isLocationSharingEnabled[appointment.id] = false;

        // Arrêter le suivi GPS temps réel
        this.stopRealTimeTracking();

        // Sauvegarder l'état dans le stockage local
        this.saveLocationSharingStates();

        // Forcer la détection de changement
        this.cdr.detectChanges();

        // Afficher une notification de succès
        this.snackBar.open(
          '🛑 Partage de position arrêté avec succès',
          'Fermer',
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('❌ Error updating backend:', error);

        // Fallback: arrêter localement même si le backend échoue
        this.locationSharingService.stopLocationSharing(appointment.id).subscribe({
          next: (success) => {
            if (success) {
              appointment.locationSharingEnabled = false;
              this.isLocationSharingEnabled[appointment.id] = false;
              console.log('⚠️ Location sharing stopped locally (backend failed)');
              this.cdr.detectChanges();

              this.snackBar.open(
                '⚠️ Partage arrêté localement (erreur serveur)',
                'Fermer',
                { duration: 5000 }
              );
            }
          },
          error: (localError) => {
            console.error('❌ Error stopping location sharing locally:', localError);
            this.snackBar.open(
              '❌ Erreur lors de l\'arrêt du partage de position',
              'Fermer',
              { duration: 5000 }
            );
          }
        });
      }
    });
  }

  // Méthode pour calculer la durée du partage de position
  getLocationSharingDuration(appointment: Appointment): string {
    if (!appointment.locationLastUpdated) {
      return 'Durée inconnue';
    }

    const startTime = new Date(appointment.locationLastUpdated);
    const now = new Date();
    const diffMs = now.getTime() - startTime.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) {
      return 'moins d\'une minute';
    } else if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const remainingMinutes = diffMinutes % 60;
      if (remainingMinutes === 0) {
        return `${hours} heure${hours > 1 ? 's' : ''}`;
      } else {
        return `${hours}h ${remainingMinutes}min`;
      }
    }
  }

  // Activer le partage de position pour un rendez-vous
  activateLocationSharing(appointment: Appointment): void {
    console.log('📡 Activating location sharing for appointment:', appointment.id);

    // Utiliser la même logique que confirmLocationSharing
    this.selectedAppointment = appointment;

    // Démarrer le partage de position
    this.locationSharingService.startLocationSharing(appointment.id).subscribe({
      next: (success) => {
        if (success === true) {
          console.log('✅ Location sharing activated successfully');

          // Mettre à jour l'état local
          this.isLocationSharingEnabled[appointment.id] = true;
          appointment.locationSharingEnabled = true;

          // Mettre à jour dans la liste
          const appointmentIndex = this.appointments.findIndex(a => a.id === appointment.id);
          if (appointmentIndex !== -1) {
            this.appointments[appointmentIndex].locationSharingEnabled = true;
          }

          // Sauvegarder l'état
          this.saveLocationSharingStates();

          // Démarrer le suivi GPS temps réel
          this.startRealTimeTracking(appointment);

          // Forcer la détection de changement
          this.cdr.detectChanges();

          // Afficher un message de succès
          this.snackBar.open(
            '✅ Partage de position activé ! Le patient peut maintenant voir votre avancement.',
            'Fermer',
            { duration: 4000 }
          );
        } else {
          console.error('❌ Failed to activate location sharing');
          this.snackBar.open(
            '❌ Erreur lors de l\'activation du partage de position',
            'Fermer',
            { duration: 3000 }
          );
        }
      },
      error: (error) => {
        console.error('❌ Error activating location sharing:', error);
        this.snackBar.open(
          '❌ Erreur lors de l\'activation du partage de position',
          'Fermer',
          { duration: 3000 }
        );
      }
    });
  }

  // Méthode de debug pour vérifier l'état complet
  debugLocationSharingState(appointmentId: number): void {
    console.log('🐛 DEBUG - Location Sharing State for appointment', appointmentId);
    console.log('🐛 isLocationSharingEnabled object:', this.isLocationSharingEnabled);
    console.log('🐛 localStorage content:', localStorage.getItem(this.LOCATION_SHARING_STORAGE_KEY));

    const appointment = this.appointments.find(a => a.id === appointmentId);
    if (appointment) {
      console.log('🐛 Appointment object:', appointment);
      console.log('🐛 appointment.locationSharingEnabled:', appointment.locationSharingEnabled);
      console.log('🐛 appointment.status:', appointment.status);
    } else {
      console.log('🐛 Appointment not found in list!');
    }

    console.log('🐛 isLocationSharingActiveForAppointment result:', this.isLocationSharingActiveForAppointment(appointmentId));
  }

  // Gestion du workflow de mission
  onSamplingCheckboxChange(appointment: Appointment, event: any): void {
    const isChecked = event.target.checked;
    console.log('📋 Sampling checkbox changed for appointment:', appointment.id, 'Checked:', isChecked);

    if (isChecked) {
      // Empêcher le changement immédiat de la checkbox
      event.target.checked = false;

      // Ouvrir la modale de confirmation
      this.samplingAppointment = appointment;
      this.showSamplingConfirmModal = true;

      console.log('🔍 Opening sampling confirmation modal for appointment:', appointment.id);
    } else {
      // Décocher n'est pas autorisé une fois que le prélèvement est effectué
      if (appointment.samplingCompleted) {
        console.log('⚠️ Cannot uncheck sampling completion');
        event.target.checked = true; // Forcer le retour à checked
      }
    }
  }

  // Méthode pour ouvrir la modale de dépôt de résultats
  uploadResults(appointment: Appointment): void {
    console.log('📤 Opening results upload modal for appointment:', appointment.id);

    this.resultsAppointment = appointment;
    this.selectedFiles = [];
    this.resultComments = '';
    this.showResultsModal = true;
  }

  // Méthode pour sauvegarder l'état samplingCompleted directement
  private saveSamplingCompletedState(appointment: Appointment): void {
    console.log('💾 Saving samplingCompleted state for appointment:', appointment.id);

    // Utiliser l'API updateAppointmentStatus pour sauvegarder l'état
    this.appointmentService.updateAppointmentStatus(appointment.id, appointment.status).subscribe({
      next: (updatedAppointment) => {
        console.log('✅ Sampling state saved successfully:', updatedAppointment);
      },
      error: (error) => {
        console.error('❌ Error saving sampling state:', error);
        // En cas d'erreur, on garde l'état local mais on informe l'utilisateur
        console.warn('⚠️ State saved locally but may not be persisted to database');
      }
    });
  }

  // Méthode pour consulter les résultats déposés (pour l'infirmier)
  viewUploadedResults(appointment: Appointment | null): void {
    if (!appointment) return;

    console.log('👁️ Viewing uploaded results for appointment:', appointment.id);

    this.resultsService.getAppointmentResults(appointment.id).subscribe({
      next: (result: AppointmentResult) => {
        this.showNurseResultsModal(appointment, result);
      },
      error: (error) => {
        console.error('❌ Error loading results:', error);
        alert('Erreur lors du chargement des résultats. Veuillez réessayer.');
      }
    });
  }

  private showNurseResultsModal(appointment: Appointment, result: AppointmentResult): void {
    const modalContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
          📊 Résultats déposés - Rendez-vous #${appointment.id}
        </h2>

        <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 15px 0;">
          <h3 style="margin-top: 0; color: #374151;">Informations du patient</h3>
          <p><strong>Patient :</strong> ${appointment.patient?.firstName} ${appointment.patient?.lastName}</p>
          <p><strong>Date du prélèvement :</strong> ${appointment.scheduledDate}</p>
          <p><strong>Adresse :</strong> ${appointment.homeAddress}</p>
          <p><strong>Analyses :</strong> ${appointment.analysisTypes?.map(a => a.name).join(', ')}</p>
        </div>

        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
          <h3 style="margin-top: 0; color: #0369a1;">Vos commentaires</h3>
          <p style="line-height: 1.6;">${result.comments || 'Aucun commentaire ajouté.'}</p>
        </div>

        <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 15px 0;">
          <h3 style="margin-top: 0; color: #059669;">Fichiers déposés (${result.files.length})</h3>
          ${result.files.map(file => `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
              <div>
                <strong>${file.fileName}</strong><br>
                <small style="color: #6b7280;">${this.resultsService.formatFileSize(file.fileSize)} • Déposé le ${new Date(result.uploadDate).toLocaleDateString('fr-FR')}</small>
              </div>
              <button onclick="window.downloadFile(${file.id}, '${file.fileName}')"
                      style="background: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                📥 Télécharger
              </button>
            </div>
          `).join('')}
        </div>

        <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 15px 0;">
          <h4 style="margin-top: 0; color: #92400e;">📋 Statut</h4>
          <p style="margin: 0;"><strong>Résultats disponibles pour le patient</strong></p>
          <small style="color: #6b7280;">Le patient a été notifié et peut maintenant consulter et télécharger ses résultats.</small>
        </div>

        <div style="text-align: center; margin-top: 20px;">
          <button onclick="window.close()"
                  style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin-right: 10px;">
            Fermer
          </button>
          <button onclick="window.downloadAllFiles()"
                  style="background: #2563eb; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">
            📥 Tout télécharger
          </button>
        </div>
      </div>
    `;

    const newWindow = window.open('', '_blank', 'width=700,height=700,scrollbars=yes,resizable=yes');
    if (newWindow) {
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Résultats déposés - Rendez-vous #${appointment.id}</title>
          <meta charset="utf-8">
        </head>
        <body>
          ${modalContent}
          <script>
            window.downloadFile = function(fileId, fileName) {
              window.opener.postMessage({action: 'downloadFile', fileId: fileId, fileName: fileName}, '*');
            };
            window.downloadAllFiles = function() {
              window.opener.postMessage({action: 'downloadAllFiles'}, '*');
            };
          </script>
        </body>
        </html>
      `);
      newWindow.document.close();

      // Écouter les messages de la fenêtre popup
      const messageHandler = (event: MessageEvent) => {
        if (event.data.action === 'downloadFile') {
          const file = result.files.find(f => f.id === event.data.fileId);
          if (file) {
            this.downloadResultFile(file);
          }
        } else if (event.data.action === 'downloadAllFiles') {
          result.files.forEach(file => this.downloadResultFile(file));
        }
      };

      window.addEventListener('message', messageHandler);

      // Nettoyer l'event listener quand la fenêtre se ferme
      const checkClosed = setInterval(() => {
        if (newWindow.closed) {
          window.removeEventListener('message', messageHandler);
          clearInterval(checkClosed);
        }
      }, 1000);
    }
  }

  private downloadResultFile(file: any): void {
    this.resultsService.downloadResultFile(file.id, file.fileName).subscribe({
      next: (blob: Blob) => {
        this.resultsService.triggerDownload(blob, file.fileName);
        console.log('✅ File downloaded by nurse:', file.fileName);
      },
      error: (error) => {
        console.error('❌ Error downloading file:', error);
        alert('Erreur lors du téléchargement. Veuillez réessayer.');
      }
    });
  }

  /**
   * Publie les résultats et envoie l'email avec PDF au patient
   */
  publishResults(appointment: Appointment): void {
    const comments = prompt('Commentaires sur les résultats (optionnel):');
    if (comments === null) return; // Utilisateur a annulé

    this.resultsService.publishResults(appointment.id!, comments || '').subscribe({
      next: (response) => {
        console.log('✅ Résultats publiés avec succès:', response);

        this.snackBar.open(
          `Résultats publiés et envoyés par email à ${response.patientEmail}`,
          'Fermer',
          { duration: 5000, panelClass: ['success-snackbar'] }
        );

        // Recharger les données pour mettre à jour le statut
        this.loadAppointments();
      },
      error: (error) => {
        console.error('❌ Erreur lors de la publication des résultats:', error);

        this.snackBar.open(
          'Erreur lors de la publication des résultats. Veuillez réessayer.',
          'Fermer',
          { duration: 5000, panelClass: ['error-snackbar'] }
        );
      }
    });
  }

  /**
   * Renvoie l'email avec les résultats au patient
   */
  resendResultsEmail(appointment: Appointment): void {
    const comments = prompt('Commentaires sur les résultats (optionnel):');
    if (comments === null) return; // Utilisateur a annulé

    this.resultsService.resendResultsEmail(appointment.id!, comments || '').subscribe({
      next: (response) => {
        console.log('✅ Email renvoyé avec succès:', response);

        this.snackBar.open(
          `Email renvoyé avec succès à ${response.patientEmail}`,
          'Fermer',
          { duration: 3000, panelClass: ['success-snackbar'] }
        );
      },
      error: (error) => {
        console.error('❌ Erreur lors du renvoi de l\'email:', error);

        this.snackBar.open(
          'Erreur lors du renvoi de l\'email. Veuillez réessayer.',
          'Fermer',
          { duration: 5000, panelClass: ['error-snackbar'] }
        );
      }
    });
  }
}
