import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';
import { User, UserUpdate } from '../../models/user.model';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="profile-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>person</mat-icon>
            Mon Profil
          </mat-card-title>
          <mat-card-subtitle>Gérez vos informations personnelles</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
            <!-- Informations de base -->
            <div class="form-section">
              <h3>Informations personnelles</h3>
              
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Prénom</mat-label>
                  <input matInput formControlName="firstName" required>
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="profileForm.get('firstName')?.hasError('required')">
                    Le prénom est requis
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Nom</mat-label>
                  <input matInput formControlName="lastName" required>
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="profileForm.get('lastName')?.hasError('required')">
                    Le nom est requis
                  </mat-error>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Téléphone</mat-label>
                <input matInput formControlName="phone" type="tel">
                <mat-icon matSuffix>phone</mat-icon>
              </mat-form-field>
            </div>

            <!-- Adresse -->
            <div class="form-section">
              <h3>Adresse</h3>
              
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Adresse complète</mat-label>
                <textarea matInput formControlName="address" rows="3"></textarea>
                <mat-icon matSuffix>location_on</mat-icon>
              </mat-form-field>
            </div>

            <!-- Informations du compte -->
            <div class="form-section">
              <h3>Informations du compte</h3>
              
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Nom d'utilisateur</mat-label>
                <input matInput [value]="currentUser?.username" readonly>
                <mat-icon matSuffix>account_circle</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput [value]="currentUser?.email" readonly>
                <mat-icon matSuffix>email</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Rôle</mat-label>
                <input matInput [value]="getRoleLabel(currentUser?.role)" readonly>
                <mat-icon matSuffix>badge</mat-icon>
              </mat-form-field>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button mat-button type="button" (click)="resetForm()">
                <mat-icon>refresh</mat-icon>
                Annuler les modifications
              </button>
              <button mat-raised-button color="primary" type="submit" 
                      [disabled]="profileForm.invalid || isLoading">
                <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
                <span *ngIf="!isLoading">Sauvegarder</span>
                <span *ngIf="isLoading">Sauvegarde...</span>
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .profile-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .form-section {
      margin-bottom: 32px;
    }

    .form-section h3 {
      margin-bottom: 16px;
      color: #333;
      font-weight: 500;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .half-width {
      flex: 1;
      margin-bottom: 16px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #eee;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .readonly-field {
      background-color: #f5f5f5;
    }
  `]
})
export class ProfileComponent implements OnInit {
  profileForm: FormGroup;
  currentUser: User | null = null;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.profileForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      phone: [''],
      address: ['']
    });
  }

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      if (user) {
        this.profileForm.patchValue({
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone || '',
          address: user.address || ''
        });
      }
    });
  }

  onSubmit(): void {
    if (this.profileForm.valid && this.currentUser) {
      this.isLoading = true;
      
      const updateData: UserUpdate = this.profileForm.value;

      // This would need to be implemented in the AuthService
      // For now, we'll just show a success message
      setTimeout(() => {
        this.isLoading = false;
        this.snackBar.open('Profil mis à jour avec succès!', 'Fermer', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
      }, 1000);
    }
  }

  resetForm(): void {
    if (this.currentUser) {
      this.profileForm.patchValue({
        firstName: this.currentUser.firstName,
        lastName: this.currentUser.lastName,
        phone: this.currentUser.phone || '',
        address: this.currentUser.address || ''
      });
    }
  }

  getRoleLabel(role?: string): string {
    switch (role) {
      case 'PATIENT': return 'Patient';
      case 'NURSE': return 'Infirmier';
      case 'ADMIN': return 'Administrateur';
      default: return '';
    }
  }
}
