import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AdminService } from '../../services/admin.service';

@Component({
  selector: 'app-admin-simple',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div style="padding: 20px;">
      <h1>Admin Simple Test</h1>
      
      <mat-card style="margin: 20px 0;">
        <mat-card-header>
          <mat-card-title>Test des endpoints</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <button mat-raised-button color="primary" (click)="testPending()" style="margin: 10px;">
            Test Pending
          </button>
          
          <button mat-raised-button color="accent" (click)="testAll()" style="margin: 10px;">
            Test All
          </button>
          
          <button mat-raised-button (click)="testNurses()" style="margin: 10px;">
            Test Nurses
          </button>
        </mat-card-content>
      </mat-card>

      <mat-card *ngIf="result" style="margin: 20px 0;">
        <mat-card-header>
          <mat-card-title>Résultat</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <pre>{{ result | json }}</pre>
        </mat-card-content>
      </mat-card>

      <mat-card *ngIf="error" style="margin: 20px 0; background: #ffebee;">
        <mat-card-header>
          <mat-card-title style="color: #c62828;">Erreur</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <pre style="color: #c62828;">{{ error | json }}</pre>
        </mat-card-content>
      </mat-card>

      <div *ngIf="pendingAppointments.length > 0">
        <h2>Rendez-vous en attente ({{ pendingAppointments.length }})</h2>
        <div *ngFor="let appointment of pendingAppointments" style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
          <h3>{{ appointment.patient?.firstName }} {{ appointment.patient?.lastName }}</h3>
          <p><strong>Date:</strong> {{ appointment.scheduledDate | date:'dd/MM/yyyy HH:mm' }}</p>
          <p><strong>Adresse:</strong> {{ appointment.homeAddress }}</p>
          <p><strong>Statut:</strong> {{ appointment.status }}</p>
          
          <button mat-raised-button color="primary" (click)="assignNurse(appointment)" style="margin: 5px;">
            Affecter automatiquement
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 300px;
      overflow-y: auto;
      background: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
    }
  `]
})
export class AdminSimpleComponent implements OnInit {
  result: any = null;
  error: any = null;
  pendingAppointments: any[] = [];

  constructor(private adminService: AdminService) {}

  ngOnInit(): void {
    console.log('AdminSimpleComponent initialisé');
  }

  testPending(): void {
    this.clearResults();
    console.log('Test pending appointments...');
    
    this.adminService.getPendingAppointments().subscribe({
      next: (data) => {
        console.log('Succès pending:', data);
        this.result = { message: 'Succès - Pending appointments', count: data.length, data: data };
        this.pendingAppointments = data;
      },
      error: (error) => {
        console.error('Erreur pending:', error);
        this.error = { message: 'Erreur pending', status: error.status, error: error.error };
      }
    });
  }

  testAll(): void {
    this.clearResults();
    console.log('Test all appointments...');
    
    this.adminService.getAllAppointments().subscribe({
      next: (data) => {
        console.log('Succès all:', data);
        this.result = { message: 'Succès - All appointments', count: data.length, data: data };
      },
      error: (error) => {
        console.error('Erreur all:', error);
        this.error = { message: 'Erreur all', status: error.status, error: error.error };
      }
    });
  }

  testNurses(): void {
    this.clearResults();
    console.log('Test nurses...');
    
    this.adminService.getAvailableNurses().subscribe({
      next: (data) => {
        console.log('Succès nurses:', data);
        this.result = { message: 'Succès - Available nurses', count: data.length, data: data };
      },
      error: (error) => {
        console.error('Erreur nurses:', error);
        this.error = { message: 'Erreur nurses', status: error.status, error: error.error };
      }
    });
  }

  assignNurse(appointment: any): void {
    console.log('Affectation automatique pour:', appointment);
    
    this.adminService.autoAssignNearestNurse(appointment.id).subscribe({
      next: (data) => {
        console.log('Affectation réussie:', data);
        this.result = { message: 'Affectation réussie', data: data };
        this.testPending(); // Recharger la liste
      },
      error: (error) => {
        console.error('Erreur affectation:', error);
        this.error = { message: 'Erreur affectation', status: error.status, error: error.error };
      }
    });
  }

  private clearResults(): void {
    this.result = null;
    this.error = null;
  }
}
