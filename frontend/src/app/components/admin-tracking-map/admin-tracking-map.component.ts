import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatRippleModule } from '@angular/material/core';

import { interval, Subscription } from 'rxjs';
import { environment } from '../../../environments/environment';

// Déclaration Leaflet
declare var L: any;

interface NursePosition {
  nurseId: number;
  nurseName: string;
  appointmentId: number;
  patientName: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  accuracy: number;
  speed?: number;
  heading?: number;
  status: string;
  estimatedArrival?: string;
}

interface PatientPosition {
  patientId: number;
  patientName: string;
  appointmentId: number;
  latitude: number;
  longitude: number;
  address: string;
  status: 'VISITED' | 'NOT_VISITED' | 'IN_PROGRESS';
  scheduledDate: string;
  nurseAssigned?: string;
}

@Component({
  selector: 'app-admin-tracking-map',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatRippleModule
  ],
  template: `
    <div class="tracking-container">
      <div class="header-section">
        <div class="header-content">
          <div class="title-section">
            <div class="title-icon">
              📍
            </div>
            <div class="title-text">
              <h1>Suivi des Infirmiers</h1>
              <p class="subtitle">{{activeNurses}} infirmier(s) actif(s) • Mise à jour en temps réel</p>
            </div>
          </div>

          <div class="status-indicator" [class.active]="isTracking">
            <div class="status-dot"></div>
            <span>{{isTracking ? 'En ligne' : 'Hors ligne'}}</span>
          </div>
        </div>
      </div>

      <div class="main-content">
        <!-- Contrôles améliorés -->
        <mat-card class="controls-card">
          <div class="controls-grid">
            <button
              mat-raised-button
              [color]="isTracking ? 'warn' : 'primary'"
              (click)="toggleTracking()"
              class="control-button primary-action"
              matRipple>
              {{isTracking ? '⏸️' : '▶️'}}
              {{isTracking ? 'Arrêter' : 'Démarrer'}} le Suivi
            </button>

            <button mat-stroked-button (click)="refreshPositions()" class="control-button" matRipple>
              🔄
              Actualiser
            </button>

            <button mat-stroked-button (click)="centerMap()" class="control-button" matRipple>
              🎯
              Centrer
            </button>
          </div>
        </mat-card>

        <!-- Statistiques améliorées -->
        <mat-card class="stats-card">
          <div class="stats-grid">
            <div class="stat-group nurses-stats">
              <div class="stat-header">
                <div class="stat-icon nurse-icon">
                  👩‍⚕️
                </div>
                <h3>Infirmiers</h3>
              </div>
              <div class="stat-chips">
                <div class="stat-chip active">
                  <span class="chip-value">{{activeNurses}}</span>
                  <span class="chip-label">Actifs</span>
                </div>
                <div class="stat-chip moving">
                  <span class="chip-value">{{movingNurses}}</span>
                  <span class="chip-label">En Route</span>
                </div>
                <div class="stat-chip delayed">
                  <span class="chip-value">{{delayedNurses}}</span>
                  <span class="chip-label">En Retard</span>
                </div>
              </div>
            </div>

            <div class="stat-group patients-stats">
              <div class="stat-header">
                <div class="stat-icon patient-icon">
                  🏠
                </div>
                <h3>Patients</h3>
              </div>
              <div class="stat-chips">
                <div class="stat-chip visited">
                  <span class="chip-value">{{visitedPatients}}</span>
                  <span class="chip-label">Visités</span>
                </div>
                <div class="stat-chip pending">
                  <span class="chip-value">{{notVisitedPatients}}</span>
                  <span class="chip-label">En Attente</span>
                </div>
              </div>
            </div>
          </div>
        </mat-card>

        <!-- Carte avec design moderne -->
        <mat-card class="map-card">
          <div class="map-header">
            <h3>Localisation en Temps Réel</h3>
          </div>

          <div class="map-container">
            <div id="admin-tracking-map" class="map"></div>

            <div *ngIf="isLoading" class="loading-overlay">
              <div class="loading-content">
                <mat-spinner diameter="40" color="primary"></mat-spinner>
                <p>Chargement des positions...</p>
              </div>
            </div>
          </div>
        </mat-card>

        <!-- Liste des infirmiers redesignée -->
        <mat-card class="nurses-card">
          <div class="nurses-header">
            <h3>Équipe Active</h3>
            <div class="nurses-counter">{{nursePositions.length}} infirmiers</div>
          </div>

          <div class="nurses-content">
            <div *ngIf="nursePositions.length === 0" class="empty-state">
              <div class="empty-icon">
                👥
              </div>
              <h4>Aucun infirmier actif</h4>
              <p>Les infirmiers apparaîtront ici dès qu'ils commenceront leur service</p>
            </div>

            <div class="nurses-grid">
              <div *ngFor="let nurse of nursePositions" class="nurse-card" [class]="'status-' + nurse.status">
                <div class="nurse-header">
                  <div class="nurse-avatar">
                    👤
                  </div>
                  <div class="nurse-identity">
                    <h4>{{nurse.nurseName}}</h4>
                    <span class="nurse-id">#{{nurse.appointmentId}}</span>
                  </div>
                  <div class="nurse-status-badge" [class]="'status-' + nurse.status">
                    {{getStatusText(nurse.status)}}
                  </div>
                </div>

                <div class="nurse-details">
                  <div class="detail-item">
                    🏥
                    <span>{{nurse.patientName}}</span>
                  </div>
                  <div class="detail-item">
                    📍
                    <span>{{nurse.latitude.toFixed(4)}}, {{nurse.longitude.toFixed(4)}}</span>
                  </div>
                  <div class="detail-item">
                    ⏰
                    <span>{{formatTime(nurse.timestamp)}}</span>
                    <span *ngIf="nurse.estimatedArrival" class="eta">ETA: {{nurse.estimatedArrival}}</span>
                  </div>
                </div>

                <div class="nurse-actions">
                  <button mat-icon-button (click)="focusOnNurse(nurse)" title="Localiser" class="action-button">
                    🎯
                  </button>
                  <button mat-icon-button (click)="showNurseRoute(nurse)" title="Itinéraire" class="action-button">
                    🗺️
                  </button>
                </div>
              </div>
            </div>
          </div>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .tracking-container {
      min-height: 100vh;
      
      padding: 20px;
    }

    .header-section {
      margin-bottom: 24px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: black;
    }

    .title-section {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .title-icon {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 12px;
      backdrop-filter: blur(10px);
    }

    .title-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .title-text h1 {
      margin: 0;
      font-size: 2.2rem;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .subtitle {
      margin: 4px 0 0 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.15);
      padding: 8px 16px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .status-indicator.active {
      background: rgba(76, 175, 80, 0.2);
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #f44336;
      animation: pulse 2s infinite;
    }

    .status-indicator.active .status-dot {
      background: #4CAF50;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .main-content {
      display: grid;
      gap: 20px;
      max-width: 1400px;
      margin: 0 auto;
    }

    /* Contrôles */
    .controls-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      border: 1px solid rgba(255,255,255,0.2);
    }

    .controls-grid {
      display: flex;
      gap: 12px;
      padding: 8px;
      flex-wrap: wrap;
    }

    .control-button {
      border-radius: 12px;
      padding: 12px 24px;
      font-weight: 500;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .primary-action {
     
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .control-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }

    /* Statistiques */
    .stats-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
      padding: 8px;
    }

    .stat-group {
      padding: 20px;
      border-radius: 12px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      transition: transform 0.3s ease;
    }

    .stat-group:hover {
      transform: translateY(-4px);
    }

    .nurses-stats {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .patients-stats {
       background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .stat-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
    }

    .stat-icon {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .stat-header h3 {
      margin: 0;
      font-size: 1.3rem;
      font-weight: 600;
    }

    .stat-chips {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .stat-chip {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      padding: 12px 16px;
      text-align: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    .stat-chip:hover {
      transform: scale(1.05);
      background: rgba(255, 255, 255, 0.3);
    }

    .chip-value {
      display: block;
      font-size: 1.8rem;
      font-weight: 700;
      line-height: 1;
    }

    .chip-label {
      font-size: 0.85rem;
      opacity: 0.9;
    }

    /* Carte */
    .map-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .map-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 20px 0;
      margin-bottom: 16px;
    }

    .map-header h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
    }

    .map-container {
      position: relative;
      height: 500px;
      margin: 0 20px 20px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .map {
      width: 100%;
      height: 100%;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      backdrop-filter: blur(5px);
    }

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }

    .loading-content p {
      margin: 0;
      color: #666;
      font-weight: 500;
    }

    /* Liste des infirmiers */
    .nurses-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .nurses-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 20px 0;
      margin-bottom: 20px;
    }

    .nurses-header h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
      font-size: 1.3rem;
    }

    .nurses-counter {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 500;
    }

    .nurses-content {
      padding: 0 20px 20px;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      background: #f5f5f5;
      border-radius: 50%;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
      font-size: 40px;
    }

    .empty-state h4 {
      margin: 0 0 8px 0;
      color: #555;
    }

    .nurses-grid {
      display: grid;
      gap: 16px;
      max-height: 600px;
      overflow-y: auto;
    }

    .nurse-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      border-left: 4px solid #ddd;
      transition: all 0.3s ease;
      position: relative;
    }

    .nurse-card:hover {
      transform: translateX(4px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    }

    .nurse-card.status-active {
      border-left-color: #4CAF50;
      background: linear-gradient(135deg, #f8fff8 0%, #e8f8e8 100%);
    }

    .nurse-card.status-moving {
      border-left-color: #2196F3;
      background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
    }

    .nurse-card.status-delayed {
      border-left-color: #f44336;
      background: linear-gradient(135deg, #fff8f8 0%, #ffebee 100%);
    }

    .nurse-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
    }

    .nurse-avatar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nurse-identity {
      flex: 1;
    }

    .nurse-identity h4 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
    }

    .nurse-id {
      font-size: 0.85rem;
      color: #666;
    }

    .nurse-status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
      text-transform: uppercase;
    }

    .nurse-status-badge.status-active {
      background: #4CAF50;
      color: white;
    }

    .nurse-status-badge.status-moving {
      background: #2196F3;
      color: white;
    }

    .nurse-status-badge.status-delayed {
      background: #f44336;
      color: white;
    }

    .nurse-details {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 16px;
    }

    .detail-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      color: #555;
    }

    .eta {
      margin-left: auto;
      color: #f57c00;
      font-weight: 500;
      font-size: 0.8rem;
    }

    .nurse-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }

    .action-button {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .action-button:hover {
      background: #667eea;
      color: white;
      transform: scale(1.1);
    }

    @media (max-width: 768px) {
      .tracking-container {
        padding: 12px;
      }

      .header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }

      .title-text h1 {
        font-size: 1.8rem;
      }

      .controls-grid {
        flex-direction: column;
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }

      .map-legend {
        flex-wrap: wrap;
        gap: 8px;
      }

      .map-container {
        height: 350px;
      }

      .nurses-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }

      .nurse-header {
        flex-wrap: wrap;
      }

      .nurse-actions {
        position: absolute;
        top: 16px;
        right: 16px;
      }
    }

    @media (max-width: 480px) {
      .nurse-card {
        padding: 16px;
      }

      .detail-item {
        font-size: 0.85rem;
      }

      .map-container {
        margin: 0 10px 20px;
        height: 300px;
      }
    }
  `]
})
export class AdminTrackingMapComponent implements OnInit, OnDestroy {
  map: any;
  nursePositions: NursePosition[] = [];
  patientPositions: PatientPosition[] = [];
  nurseMarkers: Map<number, any> = new Map();
  patientMarkers: Map<number, any> = new Map();
  isTracking = false;
  isLoading = false;
  activeNurses = 0;
  movingNurses = 0;
  delayedNurses = 0;
  visitedPatients = 0;
  notVisitedPatients = 0;

  private trackingSubscription?: Subscription;

  constructor(
    private cdr: ChangeDetectorRef,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit() {
    console.log('🚀 Initialisation du composant AdminTrackingMap...');

    // Attendre que Leaflet soit chargé et que le DOM soit prêt
    this.waitForLeaflet().then(() => {
      // Ajouter un délai pour s'assurer que le DOM est complètement rendu
      setTimeout(() => {
        this.initializeMap();
        this.startTracking();
      }, 500);
    });
  }

  private waitForLeaflet(): Promise<void> {
    return new Promise((resolve) => {
      console.log('🔍 Vérification de Leaflet...');
      if (typeof L !== 'undefined') {
        console.log('✅ Leaflet déjà disponible');
        resolve();
      } else {
        console.log('⏳ Attente du chargement de Leaflet...');
        let attempts = 0;
        const checkLeaflet = () => {
          attempts++;
          console.log(`🔄 Tentative ${attempts} de chargement de Leaflet...`);

          if (typeof L !== 'undefined') {
            console.log('✅ Leaflet chargé après', attempts, 'tentatives');
            resolve();
          } else if (attempts > 20) {
            console.error('❌ Timeout: Leaflet non chargé après 10 secondes');
            resolve(); // Continuer quand même
          } else {
            setTimeout(checkLeaflet, 500);
          }
        };
        checkLeaflet();
      }
    });
  }

  ngOnDestroy() {
    this.stopTracking();

    // Nettoyer les marqueurs
    if (this.map) {
      this.nurseMarkers.forEach(marker => this.map.removeLayer(marker));
      this.patientMarkers.forEach(marker => this.map.removeLayer(marker));
      this.nurseMarkers.clear();
      this.patientMarkers.clear();
    }
  }

  private initializeMap() {
    console.log('🗺️ Initialisation de la carte admin...');

    // Attendre que Leaflet soit chargé
    if (typeof L === 'undefined') {
      console.log('⏳ Leaflet en cours de chargement...');
      setTimeout(() => this.initializeMap(), 1000);
      return;
    }

    // Vérifier que l'élément DOM existe
    const mapElement = document.getElementById('admin-tracking-map');
    if (!mapElement) {
      console.error('❌ Élément DOM admin-tracking-map non trouvé');
      setTimeout(() => this.initializeMap(), 500);
      return;
    }

    console.log('✅ Élément DOM trouvé, création de la carte...');

    try {
      // Créer la carte Leaflet centrée sur la Tunisie
      this.map = L.map('admin-tracking-map').setView([36.8065, 10.1815], 8);

      // Ajouter les tuiles OpenStreetMap
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
      }).addTo(this.map);

      console.log('✅ Carte admin initialisée avec Leaflet');

      // Forcer un redimensionnement après un court délai
      setTimeout(() => {
        if (this.map) {
          this.map.invalidateSize();
          console.log('🔄 Taille de la carte mise à jour');
        }
      }, 100);

      // Forcer un autre redimensionnement après 1 seconde
      setTimeout(() => {
        if (this.map) {
          this.map.invalidateSize();
          console.log('🔄 Redimensionnement final de la carte');
        }
      }, 1000);

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de la carte:', error);
    }
  }

  toggleTracking() {
    if (this.isTracking) {
      this.stopTracking();
    } else {
      this.startTracking();
    }
  }

  startTracking() {
    console.log('▶️ Démarrage du suivi en temps réel...');
    this.isTracking = true;
    
    // Actualiser immédiatement
    this.refreshPositions();
    
    // Puis toutes les 5 secondes
    this.trackingSubscription = interval(5000).subscribe(() => {
      this.refreshPositions();
    });
  }

  stopTracking() {
    console.log('⏸️ Arrêt du suivi...');
    this.isTracking = false;
    
    if (this.trackingSubscription) {
      this.trackingSubscription.unsubscribe();
    }
  }

  async refreshPositions() {
    if (!this.isTracking) return;

    this.isLoading = true;

    try {
      // Appel API pour récupérer toutes les positions des infirmiers
      const nurseResponse = await fetch(`${environment.apiUrl}/tracking/all-nurses-positions`);
      const nursePositions = await nurseResponse.json();

      // Appel API pour récupérer tous les rendez-vous avec positions des patients
      // Récupération du token d'authentification depuis localStorage
      const token = localStorage.getItem('token');
      const patientResponse = await fetch(`${environment.apiUrl}/appointments/all-with-locations`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      let patientPositions = [];
      if (patientResponse.ok) {
        patientPositions = await patientResponse.json();
      } else {
        console.warn('⚠️ Erreur récupération patients:', patientResponse.status, patientResponse.statusText);
      }

      console.log('📍 Positions infirmiers reçues:', nursePositions);
      console.log('🏠 Positions patients reçues:', patientPositions);

      this.updateNursePositions(nursePositions);
      this.updatePatientPositions(patientPositions || []);

    } catch (error) {
      console.error('❌ Erreur récupération positions:', error);
      // En cas d'erreur, utiliser un tableau vide pour les patients
      this.updatePatientPositions([]);
    } finally {
      this.isLoading = false;
      this.cdr.detectChanges();
    }
  }

  private updateNursePositions(positions: NursePosition[]) {
    this.nursePositions = positions;
    this.updateStatistics();
    this.updateMapMarkers();
  }

  private updatePatientPositions(appointments: any) {
    // Vérifier que appointments est un tableau
    if (!Array.isArray(appointments)) {
      console.warn('⚠️ Données patients invalides (pas un tableau):', appointments);
      this.patientPositions = [];
      this.updateStatistics();
      return;
    }

    // Convertir les rendez-vous en positions de patients
    this.patientPositions = appointments
      .filter(apt => apt && apt.latitude && apt.longitude) // Seulement ceux avec coordonnées
      .map(apt => ({
        patientId: apt.patient?.id || 0,
        patientName: `${apt.patient?.firstName || ''} ${apt.patient?.lastName || ''}`.trim(),
        appointmentId: apt.id,
        latitude: apt.latitude,
        longitude: apt.longitude,
        address: apt.homeAddress || 'Adresse non spécifiée',
        status: this.getPatientVisitStatus(apt.status),
        scheduledDate: apt.scheduledDate,
        nurseAssigned: apt.nurse ? `${apt.nurse.firstName} ${apt.nurse.lastName}` : 'Non assigné'
      }));

    console.log('🏠 Positions patients traitées:', this.patientPositions);
    this.updateStatistics();
    this.updatePatientMarkers();
  }

  private getPatientVisitStatus(appointmentStatus: string): 'VISITED' | 'NOT_VISITED' | 'IN_PROGRESS' {
    switch (appointmentStatus) {
      case 'COMPLETED':
        return 'VISITED';
      case 'IN_PROGRESS':
      case 'NURSE_ON_WAY':
        return 'IN_PROGRESS';
      default:
        return 'NOT_VISITED';
    }
  }

  private updateStatistics() {
    // Statistiques infirmiers
    this.activeNurses = this.nursePositions.length;
    this.movingNurses = this.nursePositions.filter(n => n.status === 'ON_WAY').length;
    this.delayedNurses = this.nursePositions.filter(n => n.status === 'DELAYED').length;

    // Statistiques patients
    this.visitedPatients = this.patientPositions.filter(p => p.status === 'VISITED').length;
    this.notVisitedPatients = this.patientPositions.filter(p => p.status === 'NOT_VISITED').length;
  }

  private updateMapMarkers() {
    if (!this.map) return;

    console.log('🔄 Mise à jour des marqueurs infirmiers...', this.nursePositions.length);

    // Supprimer les anciens marqueurs d'infirmiers
    this.nurseMarkers.forEach(marker => this.map.removeLayer(marker));
    this.nurseMarkers.clear();

    // Ajouter les nouveaux marqueurs
    this.nursePositions.forEach(nurse => {
      // Créer une icône personnalisée
      const customIcon = L.divIcon({
        html: `<div style="background-color: ${this.getStatusColor(nurse.status)}; width: 30px; height: 30px; border-radius: 50%; border: 3px solid white; display: flex; align-items: center; justify-content: center; font-size: 16px;">${this.getStatusEmoji(nurse.status)}</div>`,
        className: 'custom-marker',
        iconSize: [36, 36],
        iconAnchor: [18, 18]
      });

      const marker = L.marker([nurse.latitude, nurse.longitude], {
        icon: customIcon,
        title: `${nurse.nurseName} - ${nurse.patientName}`
      }).addTo(this.map);

      // Popup avec informations
      const popupContent = this.createInfoWindowContent(nurse);
      marker.bindPopup(popupContent);

      this.nurseMarkers.set(nurse.nurseId, marker);
    });

    // Ajuster la vue pour montrer tous les marqueurs
    if (this.nursePositions.length > 0) {
      const group = new L.featureGroup(Array.from(this.nurseMarkers.values()));
      this.map.fitBounds(group.getBounds().pad(0.1));
    }
  }

  private updatePatientMarkers() {
    if (!this.map) return;

    console.log('🔄 Mise à jour des marqueurs patients...', this.patientPositions.length);

    // Supprimer les anciens marqueurs de patients
    this.patientMarkers.forEach(marker => this.map.removeLayer(marker));
    this.patientMarkers.clear();

    // Ajouter les nouveaux marqueurs de patients
    this.patientPositions.forEach(patient => {
      const icon = this.getPatientIcon(patient.status);

      const marker = L.marker([patient.latitude, patient.longitude], {
        icon: L.divIcon({
          html: icon,
          className: 'custom-patient-marker',
          iconSize: [30, 30],
          iconAnchor: [15, 15]
        })
      });

      // Popup avec informations du patient
      const popupContent = `
        <div class="patient-popup">
          <h4>🏠 ${patient.patientName}</h4>
          <p><strong>Statut:</strong> ${this.getPatientStatusText(patient.status)}</p>
          <p><strong>RDV:</strong> #${patient.appointmentId}</p>
          <p><strong>Date:</strong> ${new Date(patient.scheduledDate).toLocaleDateString()}</p>
          <p><strong>Infirmier:</strong> ${patient.nurseAssigned}</p>
          <p><strong>Adresse:</strong> ${patient.address}</p>
        </div>
      `;

      marker.bindPopup(popupContent);
      marker.addTo(this.map);

      this.patientMarkers.set(patient.patientId, marker);
    });

    console.log('✅ Marqueurs patients ajoutés:', this.patientMarkers.size);
  }

  private getPatientIcon(status: 'VISITED' | 'NOT_VISITED' | 'IN_PROGRESS'): string {
    const color = status === 'VISITED' ? '#4CAF50' :
                  status === 'IN_PROGRESS' ? '#FF9800' : '#f44336';

    return `
      <div style="
        width: 30px;
        height: 30px;
        background-color: ${color};
        border: 3px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
      ">
        <span style="color: white; font-size: 16px; font-weight: bold;">
          ${status === 'VISITED' ? '✓' : status === 'IN_PROGRESS' ? '⏳' : '!'}
        </span>
      </div>
    `;
  }

  private getPatientStatusText(status: 'VISITED' | 'NOT_VISITED' | 'IN_PROGRESS'): string {
    switch (status) {
      case 'VISITED': return '✅ Visité';
      case 'IN_PROGRESS': return '⏳ En cours';
      case 'NOT_VISITED': return '❌ Non visité';
    }
  }

  private createInfoWindowContent(nurse: NursePosition): string {
    return `
      <div style="padding: 10px; min-width: 200px;">
        <h3 style="margin: 0 0 8px 0; color: #333;">👩‍⚕️ ${nurse.nurseName}</h3>
        <p style="margin: 4px 0;"><strong>Patient:</strong> ${nurse.patientName}</p>
        <p style="margin: 4px 0;"><strong>RDV:</strong> #${nurse.appointmentId}</p>
        <p style="margin: 4px 0;"><strong>Status:</strong> ${this.getStatusText(nurse.status)}</p>
        <p style="margin: 4px 0;"><strong>Position:</strong> ${nurse.latitude.toFixed(4)}, ${nurse.longitude.toFixed(4)}</p>
        <p style="margin: 4px 0;"><strong>Précision:</strong> ±${nurse.accuracy}m</p>
        ${nurse.estimatedArrival ? `<p style="margin: 4px 0;"><strong>ETA:</strong> ${nurse.estimatedArrival}</p>` : ''}
        <p style="margin: 4px 0; font-size: 0.9em; color: #666;">
          <strong>Dernière MAJ:</strong> ${this.formatTime(nurse.timestamp)}
        </p>
      </div>
    `;
  }

  centerMap() {
    if (!this.map || this.nursePositions.length === 0) return;

    console.log('🎯 Centrage de la carte...');

    if (this.nursePositions.length === 1) {
      // Un seul infirmier : centrer sur lui
      const nurse = this.nursePositions[0];
      this.map.setView([nurse.latitude, nurse.longitude], 15);
    } else {
      // Plusieurs infirmiers : ajuster pour tous les voir
      const group = new L.featureGroup(Array.from(this.nurseMarkers.values()));
      this.map.fitBounds(group.getBounds().pad(0.1));
    }
  }

  focusOnNurse(nurse: NursePosition) {
    if (!this.map) return;

    console.log('🔍 Focus sur infirmier:', nurse.nurseName);

    // Centrer sur l'infirmier
    this.map.setView([nurse.latitude, nurse.longitude], 16);

    // Ouvrir le popup
    const marker = this.nurseMarkers.get(nurse.nurseId);
    if (marker) {
      marker.openPopup();
    }
  }

  showNurseRoute(nurse: NursePosition) {
    console.log('🛣️ Affichage itinéraire pour:', nurse.nurseName);

    // Pour l'instant, juste centrer sur l'infirmier
    // Dans une vraie application, on afficherait l'itinéraire vers le patient
    this.focusOnNurse(nurse);

    this.snackBar.open(
      `🛣️ Itinéraire vers ${nurse.patientName} (fonctionnalité en développement)`,
      'Fermer',
      { duration: 3000 }
    );
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'ON_WAY': return '#2196F3';
      case 'ARRIVED': return '#4CAF50';
      case 'DELAYED': return '#FF5722';
      case 'ASSIGNED': return '#FF9800';
      default: return '#9E9E9E';
    }
  }

  getStatusEmoji(status: string): string {
    switch (status) {
      case 'ON_WAY': return '🚗';
      case 'ARRIVED': return '✅';
      case 'DELAYED': return '⏰';
      case 'ASSIGNED': return '👤';
      default: return '👤';
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'ON_WAY': return 'En Route';
      case 'ARRIVED': return 'Arrivé';
      case 'DELAYED': return 'En Retard';
      case 'AVAILABLE': return 'Disponible';
      default: return status;
    }
  }

  formatTime(timestamp: string): string {
    return new Date(timestamp).toLocaleTimeString('fr-FR');
  }
}
