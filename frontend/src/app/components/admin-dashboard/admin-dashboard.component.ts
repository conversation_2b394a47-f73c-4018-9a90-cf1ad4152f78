import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { AdminService, AutoAssignResult } from '../../services/admin.service';
import { AdminSecurityService } from '../../services/admin-security.service';
import { AuthService } from '../../services/auth.service';
import { Appointment } from '../../models/appointment.model';
import { User, AdminPermission } from '../../models/user.model';
import { NurseSelectionDialogComponent, NurseSelectionData } from '../nurse-selection-dialog/nurse-selection-dialog.component';
import { AdminTrackingMapComponent } from '../admin-tracking-map/admin-tracking-map.component';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.Emulated,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatSnackBarModule,
    MatDialogModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatBadgeModule,
    MatTooltipModule,
    MatDividerModule,
    AdminTrackingMapComponent
  ],
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.scss']
})
export class AdminDashboardComponent implements OnInit {
  pendingAppointments: Appointment[] = [];
  confirmedAppointments: Appointment[] = [];
  allAppointments: Appointment[] = [];
  availableNurses: User[] = [];

  // Nouvelles propriétés pour la gestion des patients
  allPatients: User[] = [];
  selectedPatient: User | null = null;
  selectedPatientAppointments: Appointment[] = [];

  // Propriétés pour la gestion des infirmiers
  allNurses: User[] = [];
  selectedNurseForManagement: User | null = null;
  showAddNurseForm = false;
  addNurseForm: FormGroup;
  isSubmittingNurse = false;

  // Vue actuelle
  currentView: 'appointments' | 'patients' | 'nurses' | 'statistics' | 'tracking' = 'appointments';

  pendingCount = 0;
  confirmedCount = 0;

  // Statistiques
  statistics = {
    appointments: {
      total: 0,
      pending: 0,
      confirmed: 0,
      inProgress: 0,
      completed: 0,
      cancelled: 0
    },
    patients: {
      total: 0,
      active: 0,
      newThisMonth: 0
    },
    nurses: {
      total: 0,
      available: 0,
      busy: 0
    },
    revenue: {
      thisMonth: 0,
      lastMonth: 0,
      growth: 0
    },
    performance: {
      averageResponseTime: 0,
      completionRate: 0,
      satisfactionRate: 0
    }
  };

  showAllAppointments = false;
  isLoading = false;
  showConfirmedAppointments = true;

  // Propriétés de sécurité
  currentUser: User | null = null;
  userPermissions: AdminPermission[] = [];
  sessionWarning = false;

  // Permissions enum pour le template
  AdminPermission = AdminPermission;

  // Propriétés pour la modal de sélection d'infirmier
  showNurseModal = false;
  selectedAppointmentForNurse: Appointment | null = null;
  selectedNurse: User | null = null;
  filteredNurses: User[] = [];
  nurseSearchTerm = '';
  isAssigning = false;

  constructor(
    private adminService: AdminService,
    private adminSecurityService: AdminSecurityService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder
  ) {
    this.addNurseForm = this.formBuilder.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.pattern(/^[0-9+\-\s()]+$/)]],
      latitude: [''],
      longitude: [''],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    this.initSecurity();
    this.loadData();
  }

  private initSecurity(): void {
    // Charger l'utilisateur actuel et ses permissions
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      this.userPermissions = user?.permissions || [];
      this.cdr.detectChanges();
    });

    // Surveiller les avertissements de session
    this.adminSecurityService.sessionWarning.subscribe(warning => {
      this.sessionWarning = warning;
      if (warning) {
        this.showSessionWarning();
      }
      this.cdr.detectChanges();
    });

    // Logger l'accès au dashboard admin
    this.adminSecurityService.logSecurityEvent(
      'ADMIN_DASHBOARD_ACCESS',
      'DASHBOARD'
    ).subscribe();
  }

  private showSessionWarning(): void {
    const snackBarRef = this.snackBar.open(
      'Votre session expire dans 2 minutes. Voulez-vous la prolonger ?',
      'Prolonger',
      {
        duration: 120000, // 2 minutes
        panelClass: ['warning-snackbar']
      }
    );

    snackBarRef.onAction().subscribe(() => {
      this.adminSecurityService.extendSession().subscribe({
        next: () => {
          this.snackBar.open('Session prolongée avec succès', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: () => {
          this.snackBar.open('Erreur lors de la prolongation de session', 'Fermer', {
            duration: 3000,
            panelClass: ['error-snackbar']
          });
        }
      });
    });
  }

  // Méthodes de vérification de permissions
  hasPermission(permission: AdminPermission): boolean {
    if (this.currentUser?.isSuperAdmin) return true;
    return this.userPermissions.includes(permission);
  }

  canManageAppointments(): boolean {
    return this.hasPermission(AdminPermission.MANAGE_APPOINTMENTS);
  }

  canAssignNurses(): boolean {
    // Si l'utilisateur est super admin, il a toutes les permissions
    if (this.currentUser?.isSuperAdmin) {
      return true;
    }
    return this.hasPermission(AdminPermission.ASSIGN_NURSES);
  }

  canViewAllAppointments(): boolean {
    return this.hasPermission(AdminPermission.VIEW_ALL_APPOINTMENTS);
  }

  canManageUsers(): boolean {
    return this.hasPermission(AdminPermission.MANAGE_USERS);
  }

  loadData(): void {
    this.isLoading = true;
    this.cdr.detectChanges();

    // Charger les rendez-vous en attente
    this.adminService.getPendingAppointments().subscribe({
      next: (appointments) => {
        this.pendingAppointments = appointments;
        this.pendingCount = appointments.length;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement rendez-vous en attente:', error);
        this.snackBar.open('Erreur lors du chargement des rendez-vous en attente', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });

    // Charger tous les rendez-vous
    this.adminService.getAllAppointments().subscribe({
      next: (appointments) => {
        this.allAppointments = appointments;

        // Filtrer les rendez-vous confirmés (avec infirmier assigné)
        this.confirmedAppointments = appointments.filter(apt =>
          apt.status === 'CONFIRMED' ||
          apt.status === 'NURSE_ASSIGNED' ||
          apt.status === 'NURSE_ON_WAY' ||
          apt.status === 'IN_PROGRESS'
        );

        this.confirmedCount = this.confirmedAppointments.length;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement tous les rendez-vous:', error);
        this.confirmedAppointments = [];
        this.confirmedCount = 0;
      }
    });

    // Charger les infirmiers disponibles
    this.adminService.getAvailableNurses().subscribe({
      next: (nurses) => {
        this.availableNurses = nurses;
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement infirmiers:', error);
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });

    // Charger tous les patients
    this.loadPatients();
  }

  loadPatients(): void {
    this.adminService.getAllPatients().subscribe({
      next: (patients) => {
        this.allPatients = patients;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement patients:', error);
        this.snackBar.open('Erreur lors du chargement des patients', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  refreshData(): void {
    this.loadData();
    this.snackBar.open('Données actualisées', 'Fermer', { duration: 2000 });
  }

  autoAssignNurse(appointment: Appointment): void {
    // Vérifier les permissions
    if (!this.canAssignNurses()) {
      this.snackBar.open('Vous n\'avez pas les permissions pour assigner des infirmiers', 'Fermer', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isLoading = true;
    this.cdr.detectChanges();

    // Logger l'action de sécurité
    this.adminSecurityService.logSecurityEvent(
      'AUTO_ASSIGN_NURSE',
      'APPOINTMENT',
      { appointmentId: appointment.id, patientId: appointment.patient?.id }
    ).subscribe();

    this.adminService.autoAssignNearestNurse(appointment.id!).subscribe({
      next: () => {
        this.snackBar.open(
          `Infirmier affecté automatiquement au rendez-vous de ${appointment.patient?.firstName}`,
          'Fermer',
          { duration: 3000, panelClass: ['success-snackbar'] }
        );
        this.loadData();
      },
      error: (error) => {
        console.error('Erreur affectation automatique:', error);
        this.adminSecurityService.logSecurityEvent(
          'AUTO_ASSIGN_NURSE_FAILED',
          'APPOINTMENT',
          { appointmentId: appointment.id, error: error.message }
        ).subscribe();

        this.snackBar.open('Erreur lors de l\'affectation automatique', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  autoAssignAll(): void {
    if (this.pendingAppointments.length === 0) {
      this.snackBar.open('Aucun rendez-vous en attente à affecter', 'Fermer', { duration: 3000 });
      return;
    }

    this.isLoading = true;
    this.cdr.detectChanges();

    this.adminService.autoAssignAllPendingAppointments().subscribe({
      next: (result: AutoAssignResult) => {
        const message = `Affectation terminée: ${result.assigned} réussis, ${result.failed} échecs sur ${result.totalPending} rendez-vous`;
        
        this.snackBar.open(message, 'Fermer', {
          duration: 5000,
          panelClass: result.failed > 0 ? ['warning-snackbar'] : ['success-snackbar']
        });

        if (result.errors.length > 0) {
          console.warn('Erreurs d\'affectation:', result.errors);
        }

        this.loadData();
      },
      error: (error) => {
        console.error('Erreur affectation en lot:', error);
        this.snackBar.open('Erreur lors de l\'affectation automatique en lot', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  openNurseSelection(appointment: Appointment): void {
    const dialogData: NurseSelectionData = {
      appointment: appointment,
      availableNurses: this.availableNurses
    };

    const dialogRef = this.dialog.open(NurseSelectionDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      data: dialogData,
      disableClose: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.action === 'auto-assign') {
          this.autoAssignNurse(appointment);
        } else if (result.action === 'manual-assign' && result.nurse) {
          this.assignSpecificNurse(appointment, result.nurse);
        }
      }
    });
  }

  private assignSpecificNurse(appointment: Appointment, nurse: User): void {
    this.isLoading = true;
    this.cdr.detectChanges();

    this.adminService.assignNurseToAppointment(appointment.id!, nurse.id!).subscribe({
      next: () => {
        this.snackBar.open(
          `${nurse.firstName} ${nurse.lastName} affecté(e) au rendez-vous de ${appointment.patient?.firstName}`,
          'Fermer',
          { duration: 3000, panelClass: ['success-snackbar'] }
        );
        this.loadData();
      },
      error: (error) => {
        console.error('Erreur affectation manuelle:', error);
        this.snackBar.open('Erreur lors de l\'affectation manuelle', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'PENDING': return 'warn';
      case 'CONFIRMED': return 'primary';
      case 'IN_PROGRESS': return 'accent';
      case 'COMPLETED': return 'primary';
      case 'CANCELLED': return '';
      default: return '';
    }
  }



  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING': return 'pending';
      case 'CONFIRMED': return 'confirmed';
      case 'NURSE_ASSIGNED': return 'assigned';
      case 'NURSE_ON_WAY': return 'on-way';
      case 'IN_PROGRESS': return 'in-progress';
      case 'SAMPLING_DONE': return 'sampling-done';
      case 'COMPLETED': return 'completed';
      case 'CANCELLED': return 'cancelled';
      default: return 'default';
    }
  }

  // Nouvelles méthodes pour la gestion des vues
  setCurrentView(view: 'appointments' | 'patients' | 'nurses' | 'statistics' | 'tracking'): void {
    this.currentView = view;
    this.selectedPatient = null;
    this.selectedPatientAppointments = [];
    this.selectedNurseForManagement = null;
    this.showAddNurseForm = false;

    if (view === 'statistics') {
      this.loadStatistics();
    } else if (view === 'nurses' && this.allNurses.length === 0) {
      this.loadAllNurses();
    }

    this.cdr.detectChanges();
  }

  switchToAppointmentsView(): void {
    this.setCurrentView('appointments');
  }

  switchToPatientsView(): void {
    this.setCurrentView('patients');
  }

  switchToNursesView(): void {
    this.setCurrentView('nurses');
    if (this.allNurses.length === 0) {
      this.loadAllNurses();
    }
  }

  selectPatient(patient: User): void {
    console.log('Sélection du patient:', patient);
    this.selectedPatient = patient;
    this.loadPatientAppointments(patient.id!);
  }

  loadPatientAppointments(patientId: number): void {
    this.isLoading = true;
    this.cdr.detectChanges();

    this.adminService.getPatientAppointments(patientId).subscribe({
      next: (appointments) => {
        this.selectedPatientAppointments = appointments;
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement rendez-vous du patient:', error);
        this.snackBar.open('Erreur lors du chargement des rendez-vous du patient', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  backToPatientsList(): void {
    this.selectedPatient = null;
    this.selectedPatientAppointments = [];
    this.cdr.detectChanges();
  }

  // ========== MÉTHODES POUR LA GESTION DES INFIRMIERS ==========

  loadAllNurses(): void {
    this.isLoading = true;
    this.cdr.detectChanges();

    this.adminService.getAllNurses().subscribe({
      next: (nurses) => {
        this.allNurses = nurses;
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement infirmiers:', error);
        this.snackBar.open('Erreur lors du chargement des infirmiers', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  showAddNurseFormToggle(): void {
    this.showAddNurseForm = !this.showAddNurseForm;
    this.cdr.detectChanges();
  }

  selectNurseForManagement(nurse: User): void {
    this.selectedNurseForManagement = nurse;
    this.cdr.detectChanges();
  }

  backToNursesList(): void {
    this.selectedNurseForManagement = null;
    this.showAddNurseForm = false;
    this.cdr.detectChanges();
  }

  getNurseTotalAppointments(nurseId: number): number {
    return this.allAppointments.filter(apt => apt.nurse?.id === nurseId).length;
  }

  // ========== MÉTHODES POUR LE FORMULAIRE D'AJOUT D'INFIRMIER ==========

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  onSubmitAddNurse(): void {
    if (this.addNurseForm.valid && !this.isSubmittingNurse) {
      this.isSubmittingNurse = true;

      const formValue = this.addNurseForm.value;
      const nurseData = {
        username: formValue.username,
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        phone: formValue.phone || null,
        latitude: formValue.latitude ? parseFloat(formValue.latitude) : null,
        longitude: formValue.longitude ? parseFloat(formValue.longitude) : null,
        password: formValue.password,
        role: 'NURSE'
      };

      this.adminService.createNurse(nurseData).subscribe({
        next: (newNurse) => {
          this.snackBar.open('Infirmier ajouté avec succès', 'Fermer', {
            duration: 5000,
            panelClass: ['success-snackbar']
          });

          // Ajouter le nouvel infirmier à la liste
          this.allNurses.push(newNurse);

          // Réinitialiser le formulaire et fermer
          this.addNurseForm.reset();
          this.showAddNurseForm = false;
          this.isSubmittingNurse = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Erreur lors de l\'ajout de l\'infirmier:', error);
          console.error('Détails de l\'erreur:', {
            status: error.status,
            statusText: error.statusText,
            message: error.error?.message,
            details: error.error
          });

          let errorMessage = 'Erreur lors de l\'ajout de l\'infirmier';

          if (error.status === 400) {
            errorMessage = error.error?.message || 'Données invalides';
          } else if (error.status === 409) {
            errorMessage = 'Un utilisateur avec cet email ou nom d\'utilisateur existe déjà';
          } else if (error.status === 403) {
            errorMessage = 'Vous n\'avez pas les permissions pour créer un infirmier';
          } else if (error.error?.message) {
            errorMessage = error.error.message;
          }

          this.snackBar.open(errorMessage, 'Fermer', {
            duration: 7000,
            panelClass: ['error-snackbar']
          });
          this.isSubmittingNurse = false;
          this.cdr.detectChanges();
        }
      });
    } else {
      // Marquer tous les champs comme touchés pour afficher les erreurs
      Object.keys(this.addNurseForm.controls).forEach(key => {
        this.addNurseForm.get(key)?.markAsTouched();
      });
    }
  }

  cancelAddNurse(): void {
    this.addNurseForm.reset();
    this.showAddNurseForm = false;
    this.cdr.detectChanges();
  }

  getFieldError(fieldName: string): string {
    const field = this.addNurseForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return 'Ce champ est requis';
      }
      if (field.errors['email']) {
        return 'Email invalide';
      }
      if (field.errors['minlength']) {
        return `Minimum ${field.errors['minlength'].requiredLength} caractères`;
      }
      if (field.errors['pattern']) {
        if (fieldName === 'phone') {
          return 'Format de téléphone invalide';
        }
        if (fieldName === 'latitude') {
          return 'Latitude invalide (-90 à 90)';
        }
        if (fieldName === 'longitude') {
          return 'Longitude invalide (-180 à 180)';
        }
        return 'Format invalide';
      }
    }

    // Vérifier l'erreur de correspondance des mots de passe
    if (fieldName === 'confirmPassword' && this.addNurseForm.errors?.['passwordMismatch'] && field?.touched) {
      return 'Les mots de passe ne correspondent pas';
    }

    return '';
  }

  getPatientAppointmentCount(patientId: number): number {
    return this.allAppointments.filter(apt => apt.patient?.id === patientId).length;
  }

  getPatientPendingAppointmentCount(patientId: number): number {
    return this.allAppointments.filter(apt =>
      apt.patient?.id === patientId && apt.status === 'PENDING'
    ).length;
  }

  formatDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('fr-FR', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDateTime(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusLabel(status: string): string {
    const statusLabels: { [key: string]: string } = {
      'PENDING': 'En attente',
      'CONFIRMED': 'Confirmé',
      'NURSE_ASSIGNED': 'Infirmier assigné',
      'NURSE_ON_WAY': 'Infirmier en route',
      'IN_PROGRESS': 'En cours',
      'SAMPLING_DONE': 'Prélèvement terminé',
      'ANALYSIS_IN_PROGRESS': 'Analyse en cours',
      'RESULTS_AVAILABLE': 'Résultats disponibles',
      'COMPLETED': 'Terminé',
      'CANCELLED': 'Annulé'
    };
    return statusLabels[status] || status;
  }

  // Méthodes pour le style des rendez-vous (comme dans le dashboard patient)
  getStatusBadgeClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'PENDING': 'status-pending',
      'CONFIRMED': 'status-confirmed',
      'NURSE_ASSIGNED': 'status-assigned',
      'NURSE_ON_WAY': 'status-on-way',
      'IN_PROGRESS': 'status-in-progress',
      'SAMPLING_DONE': 'status-sampling-done',
      'ANALYSIS_IN_PROGRESS': 'status-analysis',
      'RESULTS_AVAILABLE': 'status-results',
      'COMPLETED': 'status-completed',
      'CANCELLED': 'status-cancelled'
    };
    return statusClasses[status] || 'status-default';
  }

  getProgressPercentage(status: string): number {
    const progressMap: { [key: string]: number } = {
      'PENDING': 20,
      'NURSE_ASSIGNED': 40,
      'CONFIRMED': 40,
      'NURSE_ON_WAY': 60,
      'IN_PROGRESS': 80,
      'SAMPLING_DONE': 90,
      'ANALYSIS_IN_PROGRESS': 90,
      'RESULTS_AVAILABLE': 100,
      'COMPLETED': 100,
      'CANCELLED': 0
    };
    return progressMap[status] || 0;
  }

  getStepStatus(status: string, step: number): boolean {
    const stepMap: { [key: string]: number } = {
      'PENDING': 1,
      'NURSE_ASSIGNED': 2,
      'CONFIRMED': 2,
      'NURSE_ON_WAY': 3,
      'IN_PROGRESS': 3,
      'SAMPLING_DONE': 4,
      'ANALYSIS_IN_PROGRESS': 4,
      'RESULTS_AVAILABLE': 5,
      'COMPLETED': 5,
      'CANCELLED': 0
    };
    return (stepMap[status] || 0) >= step;
  }

  // Méthode pour charger les statistiques
  loadStatistics(): void {
    this.isLoading = true;
    this.cdr.detectChanges();

    // Calculer les statistiques à partir des données existantes
    this.calculateStatistics();

    // Optionnel : charger des statistiques supplémentaires depuis le backend
    // this.adminService.getAppointmentStats().subscribe({...});

    this.isLoading = false;
    this.cdr.detectChanges();
  }

  calculateStatistics(): void {
    // Statistiques des rendez-vous
    this.statistics.appointments.total = this.allAppointments.length;
    this.statistics.appointments.pending = this.allAppointments.filter(apt => apt.status === 'PENDING').length;
    this.statistics.appointments.confirmed = this.allAppointments.filter(apt =>
      apt.status === 'CONFIRMED' || apt.status === 'NURSE_ASSIGNED'
    ).length;
    this.statistics.appointments.inProgress = this.allAppointments.filter(apt =>
      apt.status === 'IN_PROGRESS' || apt.status === 'NURSE_ON_WAY'
    ).length;
    this.statistics.appointments.completed = this.allAppointments.filter(apt => apt.status === 'COMPLETED').length;
    this.statistics.appointments.cancelled = this.allAppointments.filter(apt => apt.status === 'CANCELLED').length;

    // Statistiques des patients
    this.statistics.patients.total = this.allPatients.length;
    this.statistics.patients.active = this.allPatients.filter(patient =>
      this.allAppointments.some(apt => apt.patient?.id === patient.id)
    ).length;

    // Statistiques des infirmiers
    this.statistics.nurses.total = this.availableNurses.length;
    this.statistics.nurses.available = this.availableNurses.filter(nurse =>
      !this.allAppointments.some(apt => apt.nurse?.id === nurse.id && apt.status === 'IN_PROGRESS')
    ).length;
    this.statistics.nurses.busy = this.statistics.nurses.total - this.statistics.nurses.available;

    // Calculs de performance
    this.statistics.performance.completionRate = this.statistics.appointments.total > 0
      ? Math.round((this.statistics.appointments.completed / this.statistics.appointments.total) * 100)
      : 0;
  }

  // Méthodes pour la modal de sélection d'infirmier
  openNurseSelectionModal(appointment: Appointment): void {
    if (!this.canAssignNurses()) {
      this.snackBar.open('Vous n\'avez pas les permissions pour assigner des infirmiers', 'Fermer', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.selectedAppointmentForNurse = appointment;
    this.filteredNurses = [...this.availableNurses];
    this.selectedNurse = null;
    this.nurseSearchTerm = '';
    this.showNurseModal = true;
    this.cdr.detectChanges();
  }

  closeNurseModal(): void {
    this.showNurseModal = false;
    this.selectedAppointmentForNurse = null;
    this.selectedNurse = null;
    this.filteredNurses = [];
    this.nurseSearchTerm = '';
    this.isAssigning = false;
    this.cdr.detectChanges();
  }

  filterNurses(): void {
    if (!this.nurseSearchTerm.trim()) {
      this.filteredNurses = [...this.availableNurses];
    } else {
      const searchTerm = this.nurseSearchTerm.toLowerCase();
      this.filteredNurses = this.availableNurses.filter(nurse =>
        nurse.firstName.toLowerCase().includes(searchTerm) ||
        nurse.lastName.toLowerCase().includes(searchTerm) ||
        nurse.email.toLowerCase().includes(searchTerm)
      );
    }
    this.cdr.detectChanges();
  }

  selectNurse(nurse: User): void {
    this.selectedNurse = nurse;
    this.cdr.detectChanges();
  }

  isNurseAvailable(nurse: User): boolean {
    // Vérifier si l'infirmier n'a pas de rendez-vous en cours
    return !this.allAppointments.some(apt =>
      apt.nurse?.id === nurse.id &&
      (apt.status === 'IN_PROGRESS' || apt.status === 'NURSE_ON_WAY')
    );
  }

  getNurseActiveAppointments(nurseId: number): number {
    return this.allAppointments.filter(apt =>
      apt.nurse?.id === nurseId &&
      (apt.status === 'CONFIRMED' || apt.status === 'NURSE_ASSIGNED' ||
       apt.status === 'NURSE_ON_WAY' || apt.status === 'IN_PROGRESS')
    ).length;
  }

  getNurseRating(nurseId: number): number {
    // Pour l'instant, retourner une note aléatoire entre 4 et 5
    // Dans une vraie application, cela viendrait de la base de données
    return Math.round((Math.random() * 1 + 4) * 10) / 10;
  }

  confirmNurseAssignment(): void {
    if (!this.selectedNurse || !this.selectedAppointmentForNurse) {
      return;
    }

    this.isAssigning = true;
    this.cdr.detectChanges();

    // Logger l'action de sécurité
    this.adminSecurityService.logSecurityEvent(
      'MANUAL_ASSIGN_NURSE',
      'APPOINTMENT',
      {
        appointmentId: this.selectedAppointmentForNurse.id,
        nurseId: this.selectedNurse.id,
        patientId: this.selectedAppointmentForNurse.patient?.id
      }
    ).subscribe();

    this.adminService.assignNurseToAppointment(
      this.selectedAppointmentForNurse.id!,
      this.selectedNurse.id!
    ).subscribe({
      next: () => {
        this.snackBar.open(
          `${this.selectedNurse!.firstName} ${this.selectedNurse!.lastName} assigné(e) avec succès !`,
          'Fermer',
          { duration: 3000, panelClass: ['success-snackbar'] }
        );
        this.closeNurseModal();
        this.loadData();
      },
      error: (error) => {
        console.error('Erreur lors de l\'assignation:', error);
        this.adminSecurityService.logSecurityEvent(
          'MANUAL_ASSIGN_NURSE_FAILED',
          'APPOINTMENT',
          {
            appointmentId: this.selectedAppointmentForNurse!.id,
            nurseId: this.selectedNurse!.id,
            error: error.message
          }
        ).subscribe();

        this.snackBar.open('Erreur lors de l\'assignation de l\'infirmier', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isAssigning = false;
        this.cdr.detectChanges();
      }
    });
  }
}
