import { Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';
import { AdminGuard, AdminPermissionGuard } from './guards/admin.guard';
import { DashboardComponent } from './components/dashboard/dashboard.component';

export const routes: Routes = [
  { path: '', loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent) },
  { path: 'home', loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent) },

  // Auth routes
  {
    path: 'auth',
    children: [
      { path: 'login', loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent) },
      { path: 'register', loadComponent: () => import('./components/register/register.component').then(m => m.RegisterComponent) },
      { path: '', redirectTo: 'login', pathMatch: 'full' }
    ]
  },

  // Legacy login route (redirect to auth/login)
  { path: 'login', redirectTo: '/auth/login', pathMatch: 'full' },

  // OTP verification route
  { path: 'verify-email', loadComponent: () => import('./components/otp-verification/otp-verification.component').then(m => m.OtpVerificationComponent) },

  // Test tracking route
  { path: 'test-tracking', loadComponent: () => import('./test-tracking/test-tracking.component').then(m => m.TestTrackingComponent) },

  // Patient tracking route (public - no auth required)
  {
    path: 'patient-tracking/:appointmentId',
    loadComponent: () => import('./components/patient-tracking/patient-tracking.component').then(m => m.PatientTrackingComponent)
  },
  {
    path: 'test-dashboard',
    loadComponent: () => import('./components/test-dashboard/test-dashboard.component').then(m => m.TestDashboardComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', redirectTo: 'appointments', pathMatch: 'full' },
      { path: 'appointments', loadComponent: () => import('./components/appointments/appointments.component').then(m => m.AppointmentsComponent) },
      { path: 'new-appointment', loadComponent: () => import('./components/new-appointment/new-appointment.component').then(m => m.NewAppointmentComponent) },
      {
        path: 'admin-appointments',
        loadComponent: () => import('./components/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent),
        canActivate: [AdminGuard],
        data: { permission: 'MANAGE_APPOINTMENTS' }
      },
      {
        path: 'admin-nurses',
        loadComponent: () => import('./components/nurse-management/nurse-management.component').then(m => m.NurseManagementComponent),
        canActivate: [AdminGuard],
        data: { permission: 'MANAGE_NURSES' }
      },
      { path: 'nurse-dashboard', loadComponent: () => import('./components/nurse-dashboard/nurse-dashboard.component').then(m => m.NurseDashboardComponent) },
      { path: 'profile', loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent) },
      { path: 'notifications', loadComponent: () => import('./components/notifications/notifications.component').then(m => m.NotificationsComponent) }
    ]
  },
  { path: '**', redirectTo: '/dashboard' }
];
