package com.medical.homesampling.controller;

import com.medical.homesampling.dto.UserRegistrationDto;
import com.medical.homesampling.dto.UserUpdateDto;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/users")
@Tag(name = "User Management", description = "User management APIs")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserController {

    @Autowired
    private UserService userService;

    @PostMapping("/register")
    @Operation(summary = "Register new user", description = "Register a new user (patient by default)")
    public ResponseEntity<?> registerUser(@Valid @RequestBody UserRegistrationDto registrationDto) {
        try {
            String message = userService.registerUserTemporary(registrationDto);
            return ResponseEntity.ok(new MessageResponse(
                "Inscription réussie! Un code de vérification a été envoyé à votre email. " +
                "Veuillez vérifier votre email pour activer votre compte."
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @GetMapping("/profile")
    @Operation(summary = "Get current user profile", description = "Get the profile of the currently authenticated user")
    public ResponseEntity<User> getCurrentUserProfile(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        User fullUser = userService.getUserById(user.getId());
        return ResponseEntity.ok(fullUser);
    }

    @PutMapping("/profile")
    @Operation(summary = "Update current user profile", description = "Update the profile of the currently authenticated user")
    public ResponseEntity<?> updateCurrentUserProfile(@Valid @RequestBody UserUpdateDto updateDto, 
                                                     Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            User updatedUser = userService.updateUser(user.getId(), updateDto);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userService.getUserById(#id).username == authentication.name")
    @Operation(summary = "Get user by ID", description = "Get user details by ID")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        User user = userService.getUserById(id);
        return ResponseEntity.ok(user);
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all users", description = "Get all users (Admin only)")
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    @GetMapping("/nurses")
    @Operation(summary = "Get all nurses", description = "Get all active nurses")
    public ResponseEntity<List<User>> getAllNurses() {
        List<User> nurses = userService.getUsersByRole(Role.NURSE);
        return ResponseEntity.ok(nurses);
    }

    @GetMapping("/nurses/available")
    @Operation(summary = "Get available nurses", description = "Get all available nurses")
    public ResponseEntity<List<User>> getAvailableNurses() {
        List<User> nurses = userService.getAvailableNurses();
        return ResponseEntity.ok(nurses);
    }

    @GetMapping("/nurses/nearest")
    @Operation(summary = "Get nearest nurses", description = "Get nearest available nurses to given coordinates")
    public ResponseEntity<List<User>> getNearestNurses(@RequestParam Double latitude, 
                                                      @RequestParam Double longitude) {
        List<User> nurses = userService.getNearestAvailableNurses(latitude, longitude);
        return ResponseEntity.ok(nurses);
    }

    @PutMapping("/nurses/{id}/availability")
    @PreAuthorize("hasRole('NURSE') and @userService.getUserById(#id).username == authentication.name or hasRole('ADMIN')")
    @Operation(summary = "Update nurse availability", description = "Update nurse availability status")
    public ResponseEntity<?> updateNurseAvailability(@PathVariable Long id, 
                                                    @RequestParam Boolean isAvailable) {
        try {
            User nurse = userService.updateNurseAvailability(id, isAvailable);
            return ResponseEntity.ok(nurse);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete user", description = "Soft delete user (Admin only)")
    public ResponseEntity<?> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.ok(new MessageResponse("User deleted successfully!"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    // Message response class
    public static class MessageResponse {
        private String message;

        public MessageResponse(String message) {
            this.message = message;
        }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
