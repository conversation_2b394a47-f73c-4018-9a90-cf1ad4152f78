package com.medical.homesampling.service;

import com.medical.homesampling.dto.UserRegistrationDto;
import com.medical.homesampling.dto.UserUpdateDto;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.PendingRegistration;
import com.medical.homesampling.entity.OtpType;
import com.medical.homesampling.exception.ResourceNotFoundException;
import com.medical.homesampling.exception.UserAlreadyExistsException;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.PendingRegistrationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;

@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PendingRegistrationRepository pendingRegistrationRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private OtpService otpService;

    /**
     * Inscription temporaire - stocke les données sans créer l'utilisateur
     */
    public String registerUserTemporary(UserRegistrationDto registrationDto) {
        // Vérifier si l'utilisateur existe déjà
        if (userRepository.existsByUsername(registrationDto.getUsername())) {
            throw new UserAlreadyExistsException("Username is already taken!");
        }

        // Vérifier si l'email est déjà utilisé par un utilisateur confirmé (pas les inscriptions temporaires)
        if (userRepository.existsByEmail(registrationDto.getEmail())) {
            throw new UserAlreadyExistsException("Email is already in use!");
        }

        // Supprimer toute inscription temporaire existante pour cet email (permet la réinscription)
        pendingRegistrationRepository.deleteByEmail(registrationDto.getEmail());

        // Nettoyer les inscriptions temporaires expirées (plus de 24h)
        cleanupExpiredPendingRegistrations();

        // Créer une inscription temporaire
        PendingRegistration pendingRegistration = new PendingRegistration();
        pendingRegistration.setUsername(registrationDto.getUsername());
        pendingRegistration.setEmail(registrationDto.getEmail());
        pendingRegistration.setPassword(passwordEncoder.encode(registrationDto.getPassword()));
        pendingRegistration.setFirstName(registrationDto.getFirstName());
        pendingRegistration.setLastName(registrationDto.getLastName());
        pendingRegistration.setPhone(registrationDto.getPhone());
        pendingRegistration.setRole(registrationDto.getRole() != null ? registrationDto.getRole() : Role.PATIENT);
        pendingRegistration.setAddress(registrationDto.getAddress());
        pendingRegistration.setLatitude(registrationDto.getLatitude());
        pendingRegistration.setLongitude(registrationDto.getLongitude());

        pendingRegistrationRepository.save(pendingRegistration);

        // Envoyer le code OTP de vérification
        otpService.generateAndSendOtp(registrationDto.getEmail(), OtpType.EMAIL_VERIFICATION);

        return "Inscription temporaire créée. Vérifiez votre email pour confirmer.";
    }

    /**
     * Finalise l'inscription après vérification OTP
     */
    public User finalizeRegistration(String email) {
        System.out.println("\n🎯 FINALISATION INSCRIPTION:");
        System.out.println("   Email: " + email);

        // Récupérer l'inscription temporaire
        PendingRegistration pendingRegistration = pendingRegistrationRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Aucune inscription en attente trouvée pour cet email"));

        System.out.println("   📋 Données de l'inscription temporaire:");
        System.out.println("      - Username: " + pendingRegistration.getUsername());
        System.out.println("      - Email: " + pendingRegistration.getEmail());
        System.out.println("      - Nom: " + pendingRegistration.getFirstName() + " " + pendingRegistration.getLastName());
        System.out.println("      - Rôle: " + pendingRegistration.getRole());
        System.out.println("      - Téléphone: " + pendingRegistration.getPhone());

        // Vérifier si l'utilisateur existe déjà
        if (userRepository.existsByUsername(pendingRegistration.getUsername())) {
            System.err.println("   ❌ ERREUR: Username déjà existant: " + pendingRegistration.getUsername());
            throw new UserAlreadyExistsException("Username is already taken!");
        }

        if (userRepository.existsByEmail(pendingRegistration.getEmail())) {
            System.err.println("   ❌ ERREUR: Email déjà existant: " + pendingRegistration.getEmail());
            throw new UserAlreadyExistsException("Email is already in use!");
        }

        // Créer l'utilisateur final
        User user = new User();
        user.setUsername(pendingRegistration.getUsername());
        user.setEmail(pendingRegistration.getEmail());
        user.setPassword(pendingRegistration.getPassword()); // Déjà hashé
        user.setFirstName(pendingRegistration.getFirstName());
        user.setLastName(pendingRegistration.getLastName());
        user.setPhone(pendingRegistration.getPhone());
        user.setRole(pendingRegistration.getRole());
        user.setAddress(pendingRegistration.getAddress());
        user.setLatitude(pendingRegistration.getLatitude());
        user.setLongitude(pendingRegistration.getLongitude());
        user.setEnabled(true); // Activé après vérification
        user.setEmailVerified(true); // Email vérifié

        System.out.println("   💾 Sauvegarde de l'utilisateur...");
        User savedUser = userRepository.save(user);
        System.out.println("   ✅ Utilisateur sauvegardé avec ID: " + savedUser.getId());

        // Supprimer l'inscription temporaire
        System.out.println("   🗑️ Suppression de l'inscription temporaire...");
        pendingRegistrationRepository.deleteByEmail(email);
        System.out.println("   ✅ Inscription temporaire supprimée");

        return savedUser;
    }

    /**
     * Méthode pour la création directe d'utilisateur (admin)
     */
    public User registerUser(UserRegistrationDto registrationDto) {
        // Check if user already exists
        if (userRepository.existsByUsername(registrationDto.getUsername())) {
            throw new UserAlreadyExistsException("Username is already taken!");
        }

        if (userRepository.existsByEmail(registrationDto.getEmail())) {
            throw new UserAlreadyExistsException("Email is already in use!");
        }

        // Create new user
        User user = new User();
        user.setUsername(registrationDto.getUsername());
        user.setEmail(registrationDto.getEmail());
        user.setPassword(passwordEncoder.encode(registrationDto.getPassword()));
        user.setFirstName(registrationDto.getFirstName());
        user.setLastName(registrationDto.getLastName());
        user.setPhone(registrationDto.getPhone());
        user.setRole(registrationDto.getRole() != null ? registrationDto.getRole() : Role.PATIENT);
        user.setAddress(registrationDto.getAddress());
        user.setLatitude(registrationDto.getLatitude());
        user.setLongitude(registrationDto.getLongitude());
        user.setEnabled(true); // Activé directement pour admin
        user.setEmailVerified(true);

        return userRepository.save(user);
    }

    public User updateUser(Long userId, UserUpdateDto updateDto) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Update fields if provided
        if (updateDto.getFirstName() != null) {
            user.setFirstName(updateDto.getFirstName());
        }
        if (updateDto.getLastName() != null) {
            user.setLastName(updateDto.getLastName());
        }
        if (updateDto.getPhone() != null) {
            user.setPhone(updateDto.getPhone());
        }
        if (updateDto.getAddress() != null) {
            user.setAddress(updateDto.getAddress());
        }
        if (updateDto.getLatitude() != null) {
            user.setLatitude(updateDto.getLatitude());
        }
        if (updateDto.getLongitude() != null) {
            user.setLongitude(updateDto.getLongitude());
        }

        return userRepository.save(user);
    }

    public User getUserById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
    }

    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public List<User> getUsersByRole(Role role) {
        return userRepository.findByRoleAndEnabledTrue(role);
    }

    public List<User> getAvailableNurses() {
        return userRepository.findAvailableNurses(Role.NURSE);
    }

    public List<User> getNearestAvailableNurses(Double latitude, Double longitude) {
        return userRepository.findNearestAvailableNurses(latitude, longitude);
    }

    public User updateNurseAvailability(Long nurseId, Boolean isAvailable) {
        User nurse = getUserById(nurseId);
        if (nurse.getRole() != Role.NURSE) {
            throw new IllegalArgumentException("User is not a nurse");
        }
        nurse.setIsAvailable(isAvailable);
        return userRepository.save(nurse);
    }

    public void deleteUser(Long id) {
        User user = getUserById(id);
        user.setEnabled(false);
        userRepository.save(user);
    }

    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    public boolean existsByEmail(String email) {
        // Vérifier dans les utilisateurs confirmés ET dans les inscriptions en attente
        return userRepository.existsByEmail(email) ||
               pendingRegistrationRepository.findByEmail(email).isPresent();
    }

    /**
     * Vérifie le code OTP et active le compte utilisateur
     */
    public boolean verifyEmailOtp(String email, String otpCode) {
        System.out.println("\n🔍 VÉRIFICATION OTP:");
        System.out.println("   Email: " + email);
        System.out.println("   Code: " + otpCode);

        // Vérifier l'OTP
        boolean isValidOtp = otpService.verifyOtp(email, otpCode, OtpType.EMAIL_VERIFICATION);
        System.out.println("   OTP valide: " + isValidOtp);

        if (!isValidOtp) {
            System.out.println("   ❌ OTP invalide ou expiré");
            return false;
        }

        // Vérifier s'il s'agit d'une inscription temporaire
        Optional<PendingRegistration> pendingOpt = pendingRegistrationRepository.findByEmail(email);
        System.out.println("   Inscription temporaire trouvée: " + pendingOpt.isPresent());

        if (pendingOpt.isPresent()) {
            System.out.println("   📝 Finalisation de l'inscription...");
            try {
                User createdUser = finalizeRegistration(email);
                System.out.println("   ✅ Utilisateur créé avec succès:");
                System.out.println("      - ID: " + createdUser.getId());
                System.out.println("      - Username: " + createdUser.getUsername());
                System.out.println("      - Email: " + createdUser.getEmail());
                System.out.println("      - Rôle: " + createdUser.getRole());
                System.out.println("      - Activé: " + createdUser.getEnabled());
                System.out.println("      - Email vérifié: " + createdUser.getEmailVerified());
                return true;
            } catch (Exception e) {
                System.err.println("   ❌ Erreur lors de la finalisation: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }

        // Sinon, activer un utilisateur existant (cas admin)
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isPresent()) {
            System.out.println("   👤 Activation d'un utilisateur existant...");
            User user = userOpt.get();
            user.setEnabled(true);
            user.setEmailVerified(true);
            User savedUser = userRepository.save(user);
            System.out.println("   ✅ Utilisateur activé: " + savedUser.getUsername());
            return true;
        }

        System.out.println("   ❌ Aucun utilisateur ou inscription trouvé pour cet email");
        return false;
    }

    /**
     * Renvoie un code OTP de vérification email
     */
    public void resendEmailVerificationOtp(String email) {
        // Vérifier que l'utilisateur existe et n'est pas encore vérifié
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isEmpty()) {
            throw new ResourceNotFoundException("Utilisateur non trouvé avec cet email");
        }

        User user = userOpt.get();
        if (user.getEmailVerified()) {
            throw new IllegalStateException("Email déjà vérifié");
        }

        // Envoyer un nouveau code OTP
        otpService.generateAndSendOtp(email, OtpType.EMAIL_VERIFICATION);
    }

    // ========== MÉTHODES ADMIN ==========

    /**
     * Crée un utilisateur (pour admin)
     */
    public User createUser(UserRegistrationDto registrationDto) {
        return registerUser(registrationDto);
    }

    /**
     * Met à jour un utilisateur (pour admin)
     */
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    /**
     * Recherche des infirmiers avec filtres
     */
    public List<User> searchNurses(String search, String status, String availability) {
        List<User> nurses = getUsersByRole(Role.NURSE);

        return nurses.stream()
            .filter(nurse -> {
                // Filtre de recherche
                if (search != null && !search.trim().isEmpty()) {
                    String searchLower = search.toLowerCase();
                    boolean matchesSearch = nurse.getFirstName().toLowerCase().contains(searchLower) ||
                                          nurse.getLastName().toLowerCase().contains(searchLower) ||
                                          nurse.getEmail().toLowerCase().contains(searchLower);
                    if (!matchesSearch) return false;
                }

                // Filtre de statut
                if (status != null && !status.equals("all")) {
                    if (status.equals("active") && !nurse.isEnabled()) return false;
                    if (status.equals("inactive") && nurse.isEnabled()) return false;
                }

                // Filtre de disponibilité
                if (availability != null && !availability.equals("all")) {
                    if (availability.equals("available") && !nurse.getIsAvailable()) return false;
                    if (availability.equals("unavailable") && nurse.getIsAvailable()) return false;
                }

                return true;
            })
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Récupère les statistiques des infirmiers
     */
    public Map<String, Object> getNurseStatistics() {
        List<User> nurses = getUsersByRole(Role.NURSE);

        Map<String, Object> stats = new HashMap<>();
        stats.put("total", nurses.size());
        stats.put("active", nurses.stream().mapToInt(n -> n.isEnabled() ? 1 : 0).sum());
        stats.put("inactive", nurses.stream().mapToInt(n -> !n.isEnabled() ? 1 : 0).sum());
        stats.put("available", nurses.stream().mapToInt(n -> n.getIsAvailable() ? 1 : 0).sum());
        stats.put("unavailable", nurses.stream().mapToInt(n -> !n.getIsAvailable() ? 1 : 0).sum());

        return stats;
    }

    /**
     * Nettoie les inscriptions temporaires expirées (plus de 24h)
     */
    private void cleanupExpiredPendingRegistrations() {
        try {
            LocalDateTime expiredBefore = LocalDateTime.now().minusHours(24);
            List<PendingRegistration> expiredRegistrations = pendingRegistrationRepository.findAll()
                .stream()
                .filter(reg -> reg.getCreatedAt().isBefore(expiredBefore))
                .toList();

            if (!expiredRegistrations.isEmpty()) {
                for (PendingRegistration expired : expiredRegistrations) {
                    pendingRegistrationRepository.delete(expired);
                }
                System.out.println("🧹 Nettoyage: " + expiredRegistrations.size() + " inscriptions temporaires expirées supprimées");
            }
        } catch (Exception e) {
            System.err.println("Erreur lors du nettoyage des inscriptions expirées: " + e.getMessage());
        }
    }
}
